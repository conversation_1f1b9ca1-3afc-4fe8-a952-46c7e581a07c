'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createWorkflowService } from '@kit/comfyui/workflow-service';
import { z } from 'zod';
import { 
    CreateComfyUIWorkflowSchema, 
    UpdateComfyUIWorkflowSchema as BaseUpdateWorkflowSchema, 
    CreateComfyUIWorkflowConfigSchema, 
    UpdateComfyUIWorkflowConfigSchema as BaseUpdateConfigSchema
} from '@kit/comfyui/types';

const UpdateWorkflowSchema = BaseUpdateWorkflowSchema.extend({
    id: z.string().uuid(),
});

const UpdateConfigSchema = BaseUpdateConfigSchema.extend({
    id: z.string().uuid(),
});

/**
 * 创建工作流
 */
export const createWorkflowAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    const workflow = await workflowService.createWorkflow({
      ...data,
      author_id: user.id,
    });

    return { 
      success: true, 
      workflow: {
        ...workflow,
        workflow_json: workflow.workflow_json ?? {},
      },
      message: '工作流创建成功' 
    };
  },
  {
    auth: true,
    schema: CreateComfyUIWorkflowSchema,
  },
);

/**
 * 更新工作流
 */
export const updateWorkflowAction = enhanceAction(
  async function (data, user) {
    const { id, ...updateData } = data;
    
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    const workflow = await workflowService.updateWorkflow(id, updateData);

    return { 
      success: true, 
      workflow: {
        ...workflow,
        workflow_json: workflow.workflow_json ?? {},
      },
      message: '工作流更新成功' 
    };
  },
  {
    auth: true,
    schema: UpdateWorkflowSchema,
  },
);

/**
 * 删除工作流
 */
export const deleteWorkflowAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    await workflowService.deleteWorkflow(data.id);

    return { 
      success: true, 
      message: '工作流删除成功' 
    };
  },
  {
    auth: true,
    schema: z.object({
      id: z.string().uuid(),
    }),
  },
);

/**
 * 获取工作流列表
 */
export const getWorkflowsAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    const result = await workflowService.getWorkflows({
      isPublic: data.isPublic,
      type: data.type,
      limit: data.limit,
      offset: data.offset,
    });

    return { 
      success: true, 
      ...result 
    };
  },
  {
    auth: true,
    schema: z.object({
      isPublic: z.boolean().optional(),
      type: z.enum(['txt2img', 'img2img', 'upscale', 'inpaint', 'controlnet', 'custom']).optional(),
      limit: z.number().optional(),
      offset: z.number().optional(),
    }).optional().default({}),
  },
);

// ==================== 配置相关 Actions ====================

/**
 * 创建配置
 */
export const createConfigAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    const config = await workflowService.createWorkflowConfig(data);

    return { 
      success: true, 
      config: {
        ...config,
        node_overrides: config.node_overrides ?? {},
        default_parameters: config.default_parameters ?? {},
        parameter_mapping: config.parameter_mapping ?? {},
        parameter_limits: config.parameter_limits ?? {},
        required_parameters: config.required_parameters ?? [],
      },
      message: '配置创建成功' 
    };
  },
  {
    auth: true,
    schema: CreateComfyUIWorkflowConfigSchema,
  },
);

/**
 * 更新配置
 */
export const updateConfigAction = enhanceAction(
  async function (data, user) {
    const { id, ...updateData } = data;
    
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    const config = await workflowService.updateWorkflowConfig(id, updateData);

    return { 
      success: true, 
      config: {
        ...config,
        node_overrides: config.node_overrides ?? {},
        default_parameters: config.default_parameters ?? {},
        parameter_mapping: config.parameter_mapping ?? {},
        parameter_limits: config.parameter_limits ?? {},
        required_parameters: config.required_parameters ?? [],
      },
      message: '配置更新成功' 
    };
  },
  {
    auth: true,
    schema: UpdateConfigSchema,
  },
);

/**
 * 删除配置
 */
export const deleteConfigAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    await workflowService.deleteWorkflowConfig(data.id);

    return { 
      success: true, 
      message: '配置删除成功' 
    };
  },
  {
    auth: true,
    schema: z.object({
      id: z.string().uuid(),
    }),
  },
);