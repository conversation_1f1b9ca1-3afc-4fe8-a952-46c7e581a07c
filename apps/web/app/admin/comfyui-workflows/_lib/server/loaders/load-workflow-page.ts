import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createWorkflowService } from '@kit/comfyui/workflow-service';
import { notFound } from 'next/navigation';

export async function loadWorkflowPage(workflowId: string) {
  const client = getSupabaseServerClient();
  const workflowService = createWorkflowService(client);

  try {
    // Load workflow
    const workflow = await workflowService.getWorkflowById(workflowId);
    
    if (!workflow) {
      notFound();
    }

    // Load related configs
    const configs = await workflowService.getWorkflowConfigs({
      workflowId,
    });

    return {
      workflow,
      configs: configs.configs || [],
    };
  } catch (error) {
    console.error('Error loading workflow page:', error);
    notFound();
  }
}