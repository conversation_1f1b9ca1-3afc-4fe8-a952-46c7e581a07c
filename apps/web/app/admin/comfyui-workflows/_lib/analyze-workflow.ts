import { ParameterDefinition } from '../_components/editor/config-parameters-pane';

/**
 * @class WorkflowAnalyzer
 * @description Provides static methods to analyze a ComfyUI workflow JSON structure.
 */
export class WorkflowAnalyzer {
    /**
     * Finds all nodes of a specific class type in the workflow.
     * @param workflowJson The workflow JSON object.
     * @param nodeClass The class_type of the nodes to find.
     * @returns An array of nodes with their IDs.
     */
    static findNodesByClass(workflowJson: any, nodeClass: string): Array<{id: string, node: any}> {
        if (!workflowJson || typeof workflowJson !== 'object') return [];
        
        const results: Array<{id: string, node: any}> = [];
        
        Object.entries(workflowJson).forEach(([nodeId, node]: [string, any]) => {
            if (node && typeof node === 'object' && node.class_type === nodeClass) {
                results.push({ id: nodeId, node });
            }
        });
        
        return results;
    }

    /**
     * Finds text input nodes (CLIPTextEncode) and guesses their purpose.
     * @param workflowJson The workflow JSON object.
     * @returns An array of text nodes with their ID and guessed purpose.
     */
    static findTextInputNodes(workflowJson: any): Array<{id: string, purpose: 'prompt' | 'negative' | 'other'}> {
        const textNodes = this.findNodesByClass(workflowJson, 'CLIPTextEncode');
        const results: Array<{id: string, purpose: 'prompt' | 'negative' | 'other'}> = [];
        
        textNodes.forEach(({id, node}) => {
            const text = node?.inputs?.text ?? '';
            if (typeof text === 'string') {
                if (text.toLowerCase().includes('positive') || text.toLowerCase().includes('best quality') || text === '') {
                    results.push({id, purpose: 'prompt'});
                } else if (text.toLowerCase().includes('negative') || text.toLowerCase().includes('worst quality') || text.toLowerCase().includes('nsfw')) {
                    results.push({id, purpose: 'negative'});
                } else {
                    results.push({id, purpose: 'other'});
                }
            }
        });
        
        return results;
    }

    /**
     * Finds the primary sampler node in the workflow.
     * @param workflowJson The workflow JSON object.
     * @returns The ID of the sampler node, or null if not found.
     */
    static findSamplerNode(workflowJson: any): string | null {
        const samplers = this.findNodesByClass(workflowJson, 'KSampler');
        if (samplers.length > 0) return samplers[0]?.id ?? null;
        
        const advancedSamplers = this.findNodesByClass(workflowJson, 'KSamplerAdvanced');
        if (advancedSamplers.length > 0) return advancedSamplers[0]?.id ?? null;
        
        return null;
    }

    /**
     * Finds the latent image creation node.
     * @param workflowJson The workflow JSON object.
     * @returns The ID of the latent image node, or null if not found.
     */
    static findLatentImageNode(workflowJson: any): string | null {
        const latents = this.findNodesByClass(workflowJson, 'EmptyLatentImage');
        if (latents.length > 0) return latents[0]?.id ?? null;
        
        return null;
    }

    /**
     * Finds the primary checkpoint loader node.
     * @param workflowJson The workflow JSON object.
     * @returns The ID of the loader node, or null if not found.
     */
    static findCheckpointLoaderNode(workflowJson: any): string | null {
        // Check for the most common loader types
        const simpleLoaders = this.findNodesByClass(workflowJson, 'CheckpointLoaderSimple');
        if (simpleLoaders.length > 0) return simpleLoaders[0]?.id ?? null;

        const advancedLoaders = this.findNodesByClass(workflowJson, 'CheckpointLoader');
        if (advancedLoaders.length > 0) return advancedLoaders[0]?.id ?? null;

        return null;
    }
}

/**
 * Analyzes a workflow JSON and extracts a list of suggested parameter definitions.
 * @param workflowJson The workflow JSON to analyze.
 * @returns An array of ParameterDefinition objects.
 */
export function analyzeWorkflowForParameters(workflowJson: any): ParameterDefinition[] {
    if (!workflowJson) return [];

    const textNodes = WorkflowAnalyzer.findTextInputNodes(workflowJson);
    const samplerNodeId = WorkflowAnalyzer.findSamplerNode(workflowJson);
    const latentNodeId = WorkflowAnalyzer.findLatentImageNode(workflowJson);
    const checkpointLoaderId = WorkflowAnalyzer.findCheckpointLoaderNode(workflowJson);
    const samplerNode = samplerNodeId ? workflowJson[samplerNodeId] : null;

    const parameters: ParameterDefinition[] = [];

    const addParam = (name: string, path: string, defaultValue: any) => {
        parameters.push({
            key: `analyzed_${name}_${Math.random()}`,
            name,
            path,
            defaultValue,
        });
    };

    // Checkpoint Loader
    if (checkpointLoaderId) {
        addParam('ckpt_name', `${checkpointLoaderId}.inputs.ckpt_name`, '');
    }

    // Text prompts
    const promptNode = textNodes.find(n => n.purpose === 'prompt');
    if (promptNode) {
        addParam('prompt', `${promptNode.id}.inputs.text`, '');
    }
    const negativePromptNode = textNodes.find(n => n.purpose === 'negative');
    if (negativePromptNode) {
        addParam('negative_prompt', `${negativePromptNode.id}.inputs.text`, 'nsfw, blurry, low quality');
    }

    // Sampler settings
    if (samplerNodeId && samplerNode && samplerNode.inputs) {
        addParam('seed', `${samplerNodeId}.inputs.seed`, -1);
        addParam('steps', `${samplerNodeId}.inputs.steps`, 20);
        addParam('cfg', `${samplerNodeId}.inputs.cfg`, 7.0);
        if (samplerNode.inputs.hasOwnProperty('sampler_name')) {
            addParam('sampler_name', `${samplerNodeId}.inputs.sampler_name`, 'euler');
        }
        if (samplerNode.inputs.hasOwnProperty('scheduler')) {
            addParam('scheduler', `${samplerNodeId}.inputs.scheduler`, 'normal');
        }
        if (samplerNode.inputs.hasOwnProperty('denoise')) {
            addParam('denoise', `${samplerNodeId}.inputs.denoise`, 1.0);
        }
    }

    // Latent image settings (width, height)
    if (latentNodeId && workflowJson[latentNodeId]?.inputs) {
        addParam('width', `${latentNodeId}.inputs.width`, 1024);
        addParam('height', `${latentNodeId}.inputs.height`, 1024);
        if (workflowJson[latentNodeId].inputs.hasOwnProperty('batch_size')) {
            addParam('batch_size', `${latentNodeId}.inputs.batch_size`, 1);
        }
    }

    return parameters;
} 