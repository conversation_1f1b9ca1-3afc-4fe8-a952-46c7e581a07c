import { Database } from '~/lib/database.types';

export type WorkflowType = Database['public']['Enums']['comfyui_workflow_type'];

export const getWorkflowTypeLabel = (type: WorkflowType): string => {
  const typeLabels: Record<WorkflowType, string> = {
    txt2img: '文本到图像',
    img2img: '图像到图像',
    upscale: '图像放大',
    inpaint: '图像修复',
    controlnet: 'ControlNet',
    tts: '文本到语音',
    custom: '自定义',
  };
  
  return typeLabels[type] || type;
};