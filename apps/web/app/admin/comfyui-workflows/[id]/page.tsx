import { notFound, redirect } from 'next/navigation';
import { loadWorkflowPage } from '../_lib/server/loaders/load-workflow-page';
import { PageBody } from '@kit/ui/page';

export const generateMetadata = async ({ params }: { params: { id: string } }) => {
    return {
        title: `Workflow ${params.id}`,
    };
};

export default async function ComfyUIWorkflowPage({
    params,
}: {
    params: {
        id: string;
    };
}) {
    // Since we're using a different pattern now, redirect to the main management page
    // with the workflow ID in the URL hash or query param
    redirect(`/admin/comfyui-workflows?edit=${params.id}`);
} 