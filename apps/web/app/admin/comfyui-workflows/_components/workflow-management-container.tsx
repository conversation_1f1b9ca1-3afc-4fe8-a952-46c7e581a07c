'use client';

import { useState, useTransition, useMemo } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@kit/ui/tabs';
import { Workflow, Cog, BarChart3 } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import type { ComfyUIWorkflow, ComfyUIWorkflowConfig, ComfyUIWorkflowConfigWithWorkflow, ComfyUIConfigUsage } from '@kit/comfyui/types';

import { WorkflowEditor } from './workflow-editor';
import { WorkflowViewer } from './workflow-viewer';
import { ConfigEditor } from './config-editor';
import { ConfigViewer } from './config-viewer';
import { UsageStatistics } from './usage-statistics';
import {
    createWorkflowAction,
    updateWorkflowAction,
    deleteWorkflowAction,
    createConfigAction,
    updateConfigAction,
    deleteConfigAction
} from '../_lib/server/server-actions';
import { WorkflowList } from './workflow-list';
import { ConfigList } from './config-list';

interface WorkflowManagementContainerProps {
    initialWorkflows?: ComfyUIWorkflow[];
    initialConfigs?: ComfyUIWorkflowConfigWithWorkflow[];
    initialUsageStats?: ComfyUIConfigUsage[];
}

type MainView = 'workflows' | 'configs' | 'stats';

type ViewState =
    | { view: 'list' }
    | { view: 'view-workflow'; workflowId: string }
    | { view: 'view-config'; configId: string }
    | { view: 'edit-workflow'; workflowId?: string }
    | { view: 'edit-config'; configId?: string; prefilledWorkflowId?: string };

export function WorkflowManagementContainer({
    initialWorkflows = [],
    initialConfigs = [],
    initialUsageStats = [],
}: WorkflowManagementContainerProps) {
    const [workflows, setWorkflows] = useState<ComfyUIWorkflow[]>(initialWorkflows);
    const [configs, setConfigs] = useState<ComfyUIWorkflowConfigWithWorkflow[]>(initialConfigs);
    const [usageStats, setUsageStats] = useState<ComfyUIConfigUsage[]>(initialUsageStats);

    const [mainView, setMainView] = useState<MainView>('workflows');
    const [viewState, setViewState] = useState<ViewState>({ view: 'list' });
    const [isPending, startTransition] = useTransition();

    const currentData = useMemo(() => {
        if (viewState.view === 'view-workflow' || viewState.view === 'edit-workflow') {
            return { workflow: workflows.find(w => w.id === viewState.workflowId) };
        }
        if (viewState.view === 'view-config' || (viewState.view === 'edit-config' && viewState.configId)) {
            return { config: configs.find(c => c.id === viewState.configId) };
        }
        if (viewState.view === 'edit-config' && viewState.prefilledWorkflowId) {
            return { workflow: workflows.find(w => w.id === viewState.prefilledWorkflowId) };
        }
        return { workflow: null, config: null };
    }, [viewState, workflows, configs]);

    const handleSaveWorkflow = (data: ComfyUIWorkflow) => {
        startTransition(async () => {
            const action = data.id ? updateWorkflowAction : createWorkflowAction;
            // Convert the workflow data to the correct format for the action
            const workflowData = {
                ...data,
                workflow_json: data.workflow_json as Record<string, any>,
            };
            const result = await action(workflowData);
            if (result.success && result.workflow) {
                const updatedWorkflow: ComfyUIWorkflow = {
                    ...result.workflow,
                    workflow_json: result.workflow.workflow_json || {},
                };
                setWorkflows(current => data.id ? current.map(w => w.id === data.id ? updatedWorkflow : w) : [...current, updatedWorkflow]);
                toast.success(result.message);
                setViewState({ view: 'view-workflow', workflowId: updatedWorkflow.id });
            } else {
                toast.error(result.message || '保存工作流失败');
            }
        });
    };

    const handleSaveConfig = (data: ComfyUIWorkflowConfigWithWorkflow) => {
        startTransition(async () => {
            const action = data.id ? updateConfigAction : createConfigAction;
            const { workflow, ...configData } = data;

            // Convert the config data to the correct format for the action
            const configForAction = {
                ...configData,
                node_overrides: configData.node_overrides as Record<string, any>,
                default_parameters: configData.default_parameters as Record<string, any>,
                parameter_mapping: configData.parameter_mapping as Record<string, any>,
                parameter_limits: configData.parameter_limits as Record<string, any>,
                required_parameters: configData.required_parameters || [],
            };

            const result = await action(configForAction);

            if (result.success && result.config) {
                const associatedWorkflow = workflows.find(w => w.id === result.config!.workflow_id);
                if (!associatedWorkflow) {
                    toast.error("未能找到关联的工作流，无法更新配置列表。");
                    return;
                }
                const newConfig: ComfyUIWorkflowConfigWithWorkflow = {
                    ...(result.config as ComfyUIWorkflowConfig),
                    node_overrides: result.config.node_overrides || {},
                    default_parameters: result.config.default_parameters || {},
                    parameter_mapping: result.config.parameter_mapping || {},
                    parameter_limits: result.config.parameter_limits || {},
                    required_parameters: result.config.required_parameters || [],
                    workflow: associatedWorkflow
                };

                setConfigs(current => data.id ? current.map(c => c.id === data.id ? newConfig : c) : [...current, newConfig]);

                toast.success(result.message);
                setMainView('configs');
                setViewState({ view: 'view-config', configId: newConfig.id });
            } else {
                toast.error(result.message || '保存配置失败');
            }
        });
    };

    const handleDeleteWorkflow = (workflow: ComfyUIWorkflow) => {
        if (!confirm(`确定要删除工作流 "${workflow.display_name}" 吗？此操作将一并删除所有关联的配置。`)) return;
        startTransition(async () => {
            const result = await deleteWorkflowAction({ id: workflow.id });
            if (result.success) {
                setWorkflows(ws => ws.filter(w => w.id !== workflow.id));
                setConfigs(cs => cs.filter(c => c.workflow_id !== workflow.id));
                toast.success(result.message);
                setViewState({ view: 'list' });
            } else {
                toast.error(result.message || '删除工作流失败');
            }
        });
    };

    const handleDeleteConfig = (config: ComfyUIWorkflowConfigWithWorkflow) => {
        if (!confirm(`确定要删除配置 "${config.display_name}" 吗？`)) return;
        startTransition(async () => {
            const result = await deleteConfigAction({ id: config.id });
            if (result.success) {
                setConfigs(cs => cs.filter(c => c.id !== config.id));
                toast.success(result.message);
                setViewState({ view: 'list' });
            } else {
                toast.error(result.message || '删除配置失败');
            }
        });
    };

    const renderHeaderButtons = () => {
        if (mainView === 'workflows') {
            return <Button onClick={() => setViewState({ view: 'edit-workflow' })}>
                <Workflow className="h-4 w-4 mr-2" /> 创建工作流
            </Button>;
        }
        if (mainView === 'configs') {
            return <Button onClick={() => setViewState({ view: 'edit-config' })}>
                <Cog className="h-4 w-4 mr-2" /> 创建配置
            </Button>;
        }
        return null;
    };

    const renderContent = () => {
        const { workflow, config } = currentData;

        switch (viewState.view) {
            case 'view-workflow':
                if (!workflow) return null;
                return <WorkflowViewer
                    workflow={workflow}
                    configs={configs.filter(c => c.workflow_id === workflow.id)}
                    onEdit={() => setViewState({ view: 'edit-workflow', workflowId: workflow.id })}
                    onCreateConfig={() => { setMainView('configs'); setViewState({ view: 'edit-config', prefilledWorkflowId: workflow.id }); }}
                    onEditConfig={(cfg) => { setMainView('configs'); setViewState({ view: 'edit-config', configId: cfg.id }); }}
                    onClose={() => setViewState({ view: 'list' })}
                    onSelectConfig={(cfg) => { setMainView('configs'); setViewState({ view: 'view-config', configId: cfg.id }); }}
                />;
            case 'view-config':
                if (!config) return null;
                return <ConfigViewer
                    config={config}
                    onEdit={() => setViewState({ view: 'edit-config', configId: config.id })}
                    onClose={() => setViewState({ view: 'list' })}
                />;
            case 'edit-workflow':
                return <WorkflowEditor
                    workflow={workflow as ComfyUIWorkflow | null}
                    configs={configs.filter(c => c.workflow_id === workflow?.id)}
                    onSave={handleSaveWorkflow}
                    onCancel={() => setViewState(workflow ? { view: 'view-workflow', workflowId: workflow.id } : { view: 'list' })}
                    isPending={isPending}
                />;
            case 'edit-config':
                return <ConfigEditor
                    config={currentData.config as ComfyUIWorkflowConfigWithWorkflow | null}
                    workflows={workflows}
                    onSave={handleSaveConfig}
                    onCancel={() => setViewState(currentData.config ? { view: 'view-config', configId: currentData.config.id } : { view: 'list' })}
                    isPending={isPending}
                    prefilledWorkflowId={viewState.view === 'edit-config' ? viewState.prefilledWorkflowId : undefined}
                />;
            default: // 'list'
                if (mainView === 'workflows') {
                    return <WorkflowList
                        workflows={workflows}
                        configs={configs}
                        onViewWorkflow={(wf) => setViewState({ view: 'view-workflow', workflowId: wf.id })}
                        onEditWorkflow={(wf) => setViewState({ view: 'edit-workflow', workflowId: wf.id })}
                        onDeleteWorkflow={handleDeleteWorkflow}
                        onAddWorkflow={() => setViewState({ view: 'edit-workflow' })}
                    />;
                }
                if (mainView === 'configs') {
                    return <ConfigList
                        configs={configs}
                        onViewConfig={(cfg) => setViewState({ view: 'view-config', configId: cfg.id })}
                        onEditConfig={(cfg) => setViewState({ view: 'edit-config', configId: cfg.id })}
                        onDeleteConfig={handleDeleteConfig}
                    />;
                }
                return null;
        }
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle>ComfyUI 管理</CardTitle>
                    <div className="flex items-center gap-2 min-h-[40px]">
                        {renderHeaderButtons()}
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <Tabs value={mainView} onValueChange={(v) => { setMainView(v as MainView); setViewState({ view: 'list' }); }} className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="workflows">
                            <Workflow className="h-4 w-4 mr-2" /> 工作流
                        </TabsTrigger>
                        <TabsTrigger value="configs">
                            <Cog className="h-4 w-4 mr-2" /> 配置
                        </TabsTrigger>
                        <TabsTrigger value="stats">
                            <BarChart3 className="h-4 w-4 mr-2" /> 使用统计
                        </TabsTrigger>
                    </TabsList>
                    <div className="min-h-[600px] relative pt-6">
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={mainView + viewState.view + (('workflowId' in viewState && viewState.workflowId) || ('configId' in viewState && viewState.configId) || '')}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.2, ease: 'easeInOut' }}
                                className="w-full"
                            >
                                {mainView === 'stats' ? <UsageStatistics stats={usageStats} configs={configs} onClose={() => { setMainView('workflows'); setViewState({ view: 'list' }); }} /> : renderContent()}
                            </motion.div>
                        </AnimatePresence>
                    </div>
                </Tabs>
            </CardContent>
        </Card>
    );
} 