'use client';

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@kit/ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { ArrowLeft, Edit, Settings, FileText, Database, Target, SlidersHorizontal, ListTree, CheckCircle, Share2 } from 'lucide-react';
import type { ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';

import { DescriptionItem, DescriptionList } from './shared/description-list';
import { getWorkflowTypeLabel, type WorkflowType } from '../_lib/workflow-type-utils';

interface ConfigViewerProps {
    config: ComfyUIWorkflowConfigWithWorkflow;
    onEdit: () => void;
    onClose: () => void;
}

export function ConfigViewer({ config, onEdit, onClose }: ConfigViewerProps) {
    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
            active: 'default',
            inactive: 'secondary',
            deprecated: 'destructive',
        };
        const labels: Record<string, string> = {
            active: '活跃',
            inactive: '未激活',
            deprecated: '已弃用'
        };
        return <Badge variant={variants[status] || 'secondary'}>{labels[status] || status}</Badge>;
    };

    const getTypeBadge = (type: string) => {
        return <Badge variant="outline">{getWorkflowTypeLabel(type as WorkflowType)}</Badge>;
    };

    const renderJsonOrValue = (value: any) => {
        if (typeof value === 'object' && value !== null) {
            return (
                <pre className="text-xs bg-muted p-2 rounded-md mt-1 overflow-auto">
                    {JSON.stringify(value, null, 2)}
                </pre>
            );
        }
        return <span className="font-mono text-sm">{String(value)}</span>;
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="icon" onClick={onClose}>
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <div className="flex items-center gap-3">
                            <h1 className="text-2xl font-bold">{config.display_name}</h1>
                            <div className="flex items-center gap-2">
                                {getStatusBadge(config.status)}
                                {config.is_featured && <Badge variant="secondary">推荐</Badge>}
                                {config.is_public && <Badge variant="outline">公开</Badge>}
                            </div>
                        </div>
                        <p className="text-muted-foreground">{config.description || '此配置暂无描述。'}</p>
                    </div>
                </div>
                <Button onClick={onEdit} className="gap-2">
                    <Edit className="h-4 w-4" />
                    编辑配置
                </Button>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>配置详情</CardTitle>
                    <CardDescription>关于此配置的所有信息概览。</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                    <DescriptionList title="基本信息">
                        <DescriptionItem label="显示名称">{config.display_name}</DescriptionItem>
                        <DescriptionItem label="内部名称">{config.config_name}</DescriptionItem>
                        <DescriptionItem label="关联工作流">
                            <div className="flex items-center gap-2">
                                <span>{config.workflow.display_name}</span> {getTypeBadge(config.workflow.workflow_type)}
                            </div>
                        </DescriptionItem>
                        <DescriptionItem label="排序权重">{config.display_order}</DescriptionItem>
                    </DescriptionList>

                    <DescriptionList title="技术参数">
                        <DescriptionItem label="预计处理时间">{config.estimated_time_seconds ? `${config.estimated_time_seconds}s` : '未指定'}</DescriptionItem>
                        <DescriptionItem label="GPU内存需求">{config.gpu_memory_mb ? `${config.gpu_memory_mb}MB` : '未指定'}</DescriptionItem>
                    </DescriptionList>

                    <DescriptionList title="元数据">
                        <DescriptionItem label="创建时间">{new Date(config.created_at).toLocaleString('zh-CN')}</DescriptionItem>
                        <DescriptionItem label="最后更新">{new Date(config.updated_at).toLocaleString('zh-CN')}</DescriptionItem>
                    </DescriptionList>

                    <h3 className="font-semibold text-lg mt-6 mb-4">描述信息</h3>
                    <DescriptionList>
                        <DescriptionItem label="配置描述">
                            {config.description || '无'}
                        </DescriptionItem>
                    </DescriptionList>

                    <Accordion type="multiple" className="w-full space-y-4">
                        <Card className="shadow-none border">
                            <AccordionItem value="parameters" className="border-b-0">
                                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                    <div className="flex items-center gap-3">
                                        <SlidersHorizontal className="h-5 w-5" />
                                        <div>
                                            <h3 className="font-semibold">参数映射</h3>
                                            <p className="text-sm text-muted-foreground font-normal">用户友好名称到节点路径的映射</p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-6 pb-4">
                                    {config.parameter_mapping && Object.keys(config.parameter_mapping).length > 0 ? (
                                        <div className="divide-y">
                                            {Object.entries(config.parameter_mapping).map(([key, value]) => (
                                                <div key={key} className="grid grid-cols-2 gap-4 py-3">
                                                    <div className="text-sm font-medium">{key}</div>
                                                    <div className="text-sm font-mono text-muted-foreground">{value}</div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : <p className="text-sm text-muted-foreground">无参数映射</p>}
                                </AccordionContent>
                            </AccordionItem>
                        </Card>

                        <Card className="shadow-none border">
                            <AccordionItem value="overrides" className="border-b-0">
                                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                    <div className="flex items-center gap-3">
                                        <ListTree className="h-5 w-5" />
                                        <div>
                                            <h3 className="font-semibold">节点覆盖</h3>
                                            <p className="text-sm text-muted-foreground font-normal">直接覆盖工作流中的节点值</p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-6 pb-4">
                                    {config.node_overrides && Object.keys(config.node_overrides).length > 0 ? (
                                        <div className="space-y-3">
                                            {Object.entries(config.node_overrides).map(([node_id, fields], index) => (
                                                <div key={index} className="p-3 rounded-lg border bg-muted/50">
                                                    <h4 className="font-semibold mb-2">节点ID: <span className="font-mono">{node_id}</span></h4>
                                                    {Object.entries(fields).map(([field_path, value]) => (
                                                        <div key={field_path} className="grid grid-cols-2 gap-4 py-2 border-t">
                                                            <DescriptionItem label="字段路径">{field_path}</DescriptionItem>
                                                            <DescriptionItem label="覆盖值">{renderJsonOrValue(value)}</DescriptionItem>
                                                        </div>
                                                    ))}
                                                </div>
                                            ))}
                                        </div>
                                    ) : <p className="text-sm text-muted-foreground">无节点覆盖</p>}
                                </AccordionContent>
                            </AccordionItem>
                        </Card>

                        <Card className="shadow-none border">
                            <AccordionItem value="outputs" className="border-b-0">
                                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                    <div className="flex items-center gap-3">
                                        <Share2 className="h-5 w-5" />
                                        <div>
                                            <h3 className="font-semibold">输出节点配置</h3>
                                            <p className="text-sm text-muted-foreground font-normal">工作流输出节点的映射配置</p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-6 pb-4">
                                    {config.output_keys && config.output_keys.length > 0 ? (
                                        <div className="space-y-3">
                                            <div className="grid grid-cols-1 gap-3">
                                                <div>
                                                    <h4 className="font-semibold mb-2">输出键列表</h4>
                                                    <div className="flex flex-wrap gap-2">
                                                        {config.output_keys.map((key) => (
                                                            <Badge key={key} variant="secondary">
                                                                {key}
                                                            </Badge>
                                                        ))}
                                                    </div>
                                                </div>
                                                
                                                {config.output_mapping && Object.keys(config.output_mapping).length > 0 && (
                                                    <div>
                                                        <h4 className="font-semibold mb-2">节点映射</h4>
                                                        <div className="divide-y">
                                                            {Object.entries(config.output_mapping).map(([key, nodeId]) => (
                                                                <div key={key} className="grid grid-cols-2 gap-4 py-3">
                                                                    <div className="text-sm font-medium">{key}</div>
                                                                    <div className="text-sm font-mono text-muted-foreground">节点 {nodeId}</div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ) : <p className="text-sm text-muted-foreground">无输出节点配置</p>}
                                </AccordionContent>
                            </AccordionItem>
                        </Card>

                        <Card className="shadow-none border">
                            <AccordionItem value="defaults" className="border-b-0">
                                <AccordionTrigger className="px-6 py-4 hover:no-underline">
                                    <div className="flex items-center gap-3">
                                        <CheckCircle className="h-5 w-5" />
                                        <div>
                                            <h3 className="font-semibold">默认参数</h3>
                                            <p className="text-sm text-muted-foreground font-normal">生成时使用的默认参数值</p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-6 pb-4">
                                    {config.default_parameters && Object.keys(config.default_parameters).length > 0 ? (
                                        <div className="space-y-3">
                                            {Object.entries(config.default_parameters).map(([key, value]) => (
                                                <div key={key} className="p-3 rounded-lg border bg-muted/50 grid grid-cols-2 gap-4">
                                                    <DescriptionItem label="参数名">{key}</DescriptionItem>
                                                    <DescriptionItem label="默认值">{renderJsonOrValue(value)}</DescriptionItem>
                                                </div>
                                            ))}
                                        </div>
                                    ) : <p className="text-sm text-muted-foreground">无默认参数</p>}
                                </AccordionContent>
                            </AccordionItem>
                        </Card>
                    </Accordion>
                </CardContent>
            </Card>
        </div>
    );
} 