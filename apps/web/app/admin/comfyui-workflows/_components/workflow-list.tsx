'use client';

import { Card } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Eye, Settings, Trash2 } from 'lucide-react';
import type { ComfyUIWorkflow, ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';

interface WorkflowListProps {
    workflows: ComfyUIWorkflow[];
    configs: ComfyUIWorkflowConfigWithWorkflow[];
    onViewWorkflow: (workflow: ComfyUIWorkflow) => void;
    onEditWorkflow: (workflow: ComfyUIWorkflow) => void;
    onDeleteWorkflow: (workflow: ComfyUIWorkflow) => void;
    onAddWorkflow: () => void;
}

export function WorkflowList({
    workflows,
    configs,
    onViewWorkflow,
    onEditWorkflow,
    onDeleteWorkflow
}: WorkflowListProps) {
    return (
        <div className="space-y-4">
            {workflows.map(workflow => (
                <Card key={workflow.id} className="flex items-center justify-between p-4 transition-shadow hover:shadow-md">
                    <div className="flex-1 min-w-0">
                        <h3 className="font-semibold truncate">{workflow.display_name}</h3>
                        <p className="text-sm text-muted-foreground truncate">{workflow.description || '无描述'}</p>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                        <Badge variant="outline">{configs.filter(c => c.workflow_id === workflow.id).length} 配置</Badge>
                        <Button variant="ghost" size="sm" onClick={() => onViewWorkflow(workflow)}>
                            <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => onEditWorkflow(workflow)}>
                            <Settings className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => onDeleteWorkflow(workflow)} className="text-destructive hover:text-destructive">
                            <Trash2 className="h-4 w-4" />
                        </Button>
                    </div>
                </Card>
            ))}
        </div>
    );
} 