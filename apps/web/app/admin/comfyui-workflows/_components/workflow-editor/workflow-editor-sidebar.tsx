'use client';

import { useFormContext } from 'react-hook-form';
import { Button } from '@kit/ui/button';
import { ArrowLeft, Save, Cog, FileJson, type LucideIcon } from 'lucide-react';

export type WorkflowEditorPane = 'general' | 'json';

const panes: { id: WorkflowEditorPane; label: string; Icon: LucideIcon }[] = [
    { id: 'general', label: '基本信息', Icon: Cog },
    { id: 'json', label: 'JSON 编辑器', Icon: FileJson },
];

interface WorkflowEditorSidebarProps {
    activeTab: WorkflowEditorPane;
    setActiveTab: (pane: WorkflowEditorPane) => void;
    onCancel: () => void;
    isPending?: boolean;
    isEditing?: boolean;
}

export function WorkflowEditorSidebar({ activeTab, setActiveTab, onCancel, isPending, isEditing }: WorkflowEditorSidebarProps) {
    const { formState } = useFormContext();

    return (
        <div className="flex flex-col h-full">
            <div className="flex items-center mb-6">
                <Button variant="ghost" size="icon" onClick={onCancel} className="mr-2">
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <div>
                    <h1 className="text-xl font-bold">{isEditing ? '编辑工作流' : '创建工作流'}</h1>
                </div>
            </div>

            <div className="flex-1 space-y-2">
                {panes.map((pane) => (
                    <Button
                        key={pane.id}
                        type="button"
                        variant={activeTab === pane.id ? 'secondary' : 'ghost'}
                        onClick={() => setActiveTab(pane.id)}
                        className="w-full justify-start gap-2"
                        disabled={!formState.isValid && pane.id !== 'general'}
                    >
                        <pane.Icon className="h-4 w-4" />
                        {pane.label}
                    </Button>
                ))}
            </div>

            <div className="mt-auto">
                <Button type="submit" className="w-full gap-2" disabled={isPending || !formState.isValid}>
                    <Save className="h-4 w-4" />
                    {isPending ? '保存中...' : (isEditing ? '更新工作流' : '创建工作流')}
                </Button>
            </div>
        </div>
    );
}
