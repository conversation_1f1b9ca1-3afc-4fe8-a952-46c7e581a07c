'use client';

import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Textarea } from '@kit/ui/textarea';
import { Button } from '@kit/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@kit/ui/form';
import { Check } from 'lucide-react';
import { toast } from '@kit/ui/sonner';

export function WorkflowJsonPane() {
    const { control, setValue, getValues } = useFormContext();
    const [jsonText, setJsonText] = useState(() => {
        const workflowJson = getValues('workflow_json');
        return JSON.stringify(workflowJson || {}, null, 2);
    });

    const validateAndFormatJson = () => {
        try {
            const parsed = JSON.parse(jsonText);
            const formatted = JSON.stringify(parsed, null, 2);
            setJsonText(formatted);
            setValue('workflow_json', parsed, { shouldValidate: true, shouldDirty: true });
            toast.success('JSON 格式正确并已更新');
        } catch (e) {
            toast.error('JSON 格式错误，请检查语法');
        }
    };

    const handleJsonChange = (value: string) => {
        setJsonText(value);
        try {
            const parsed = JSON.parse(value);
            setValue('workflow_json', parsed, { shouldValidate: true, shouldDirty: true });
        } catch (e) {
            // Invalid JSON, but we don't show error until validation
        }
    };

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle>工作流 JSON 编辑器</CardTitle>
                        <CardDescription>直接编辑 ComfyUI 工作流的 JSON 定义。</CardDescription>
                    </div>
                    <Button
                        type="button"
                        variant="outline"
                        onClick={validateAndFormatJson}
                        className="gap-2"
                    >
                        <Check className="h-4 w-4" />
                        验证和格式化
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                <FormField
                    name="workflow_json"
                    control={control}
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <Textarea
                                    value={jsonText}
                                    onChange={(e) => handleJsonChange(e.target.value)}
                                    placeholder="在此处粘贴或编写工作流 JSON..."
                                    className="min-h-[500px] font-mono text-xs"
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
            </CardContent>
        </Card>
    );
}
