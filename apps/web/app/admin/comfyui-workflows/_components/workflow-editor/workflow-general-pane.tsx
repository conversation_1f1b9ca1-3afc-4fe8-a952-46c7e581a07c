'use client';

import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { AnimatePresence, motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';
import { Textarea } from '@kit/ui/textarea';
import { Label } from '@kit/ui/label';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Plus, X } from 'lucide-react';

interface WorkflowGeneralPaneProps {
    tags: string[];
    setTags: (tags: string[]) => void;
}

export function WorkflowGeneralPane({ tags, setTags }: WorkflowGeneralPaneProps) {
    const form = useFormContext();
    const [newTag, setNewTag] = useState('');

    const addTag = () => {
        if (newTag.trim() && !tags.includes(newTag.trim())) {
            setTags([...tags, newTag.trim()]);
            setNewTag('');
        }
    };

    const removeTag = (tagToRemove: string) => {
        setTags(tags.filter(tag => tag !== tagToRemove));
    };

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>基本信息</CardTitle>
                    <CardDescription>工作流的基本元数据和设置</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>工作流名称</FormLabel>
                                    <FormControl>
                                        <Input placeholder="例如：txt2img_basic" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="display_name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>显示名称</FormLabel>
                                    <FormControl>
                                        <Input placeholder="例如：基础文本到图像" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                            control={form.control}
                            name="workflow_type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>工作流类型</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="选择工作流类型" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="txt2img">文本到图像</SelectItem>
                                            <SelectItem value="img2img">图像到图像</SelectItem>
                                            <SelectItem value="upscale">图像放大</SelectItem>
                                            <SelectItem value="inpaint">图像修复</SelectItem>
                                            <SelectItem value="controlnet">ControlNet</SelectItem>
                                            <SelectItem value="tts">文本到语音</SelectItem>
                                            <SelectItem value="custom">自定义</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="version"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>版本</FormLabel>
                                    <FormControl>
                                        <Input placeholder="例如：1.0.0" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>描述</FormLabel>
                                <FormControl>
                                    <Textarea
                                        placeholder="描述这个工作流的功能和用途..."
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormItem>
                        <FormLabel>公开工作流</FormLabel>
                        <FormControl>
                            <Switch
                                checked={form.watch('is_public')}
                                onCheckedChange={(value) => form.setValue('is_public', value)}
                            />
                        </FormControl>
                    </FormItem>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>标签管理</CardTitle>
                    <CardDescription>为工作流添加标签以便分类和搜索。</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 p-6">
                    <div className="flex items-center gap-2">
                        <Input
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="添加新标签..."
                            onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); addTag(); } }}
                        />
                        <Button type="button" onClick={addTag}><Plus className="h-4 w-4" /></Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                        <AnimatePresence>
                            {tags.map((tag) => (
                                <motion.div
                                    key={tag}
                                    layout
                                    initial={{ opacity: 0, scale: 0.5 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.5 }}
                                >
                                    <Badge variant="secondary" className="flex items-center gap-1">
                                        {tag}
                                        <button type="button" onClick={() => removeTag(tag)} className="rounded-full hover:bg-muted-foreground/20">
                                            <X className="h-3 w-3" />
                                        </button>
                                    </Badge>
                                </motion.div>
                            ))}
                        </AnimatePresence>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
