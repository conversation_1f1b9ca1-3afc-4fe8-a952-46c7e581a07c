'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion, AnimatePresence } from 'framer-motion';
import { ComfyUIWorkflow } from '@kit/comfyui/types';
import { Tables } from '@kit/supabase/database';
import { z } from 'zod';
import { Database } from '~/lib/database.types';
import { WorkflowEditorSidebar, type WorkflowEditorPane } from './workflow-editor/workflow-editor-sidebar';
import { WorkflowGeneralPane } from './workflow-editor/workflow-general-pane';
import { WorkflowJsonPane } from './workflow-editor/workflow-json-pane';

const formSchema = z.object({
    name: z.string().min(1, '工作流名称不能为空'),
    display_name: z.string().min(1, '显示名称不能为空'),
    description: z.string().nullable().optional(),
    workflow_type: z.custom<Database['public']['Enums']['comfyui_workflow_type']>(),
    version: z.string().nullable().optional(),
    is_public: z.boolean(),
    workflow_json: z.any(),
});

type FormSchemaType = z.infer<typeof formSchema>;

type ComfyUIWorkflowConfig = Tables<'comfyui_workflow_configs'>;

interface WorkflowEditorProps {
    workflow: ComfyUIWorkflow | null;
    configs: ComfyUIWorkflowConfig[];
    onSave: (workflow: ComfyUIWorkflow) => void;
    onCancel: () => void;
    isPending?: boolean;
}

export function WorkflowEditor({ workflow, configs, onSave, onCancel, isPending }: WorkflowEditorProps) {
    const [activeTab, setActiveTab] = useState<WorkflowEditorPane>('general');
    const [tags, setTags] = useState<string[]>(workflow?.tags ?? []);
    const isEditing = !!workflow?.id;

    const form = useForm<FormSchemaType>({
        resolver: zodResolver(formSchema),
        mode: 'onChange',
        defaultValues: {
            name: workflow?.name || '',
            display_name: workflow?.display_name || '',
            description: workflow?.description || '',
            workflow_type: workflow?.workflow_type || 'txt2img',
            version: workflow?.version || '1.0.0',
            is_public: workflow?.is_public ?? true,
            workflow_json: workflow?.workflow_json as any || {},
        },
    });

    useEffect(() => {
        if (workflow) {
            form.reset({
                name: workflow.name || '',
                display_name: workflow.display_name || '',
                description: workflow.description || '',
                workflow_type: workflow.workflow_type || 'txt2img',
                version: workflow.version || '1.0.0',
                is_public: workflow.is_public ?? true,
                workflow_json: workflow.workflow_json as any || {},
            });
            setTags(workflow.tags ?? []);
        }
    }, [workflow, form]);

    const onSubmit = (data: FormSchemaType) => {
        const workflowData: ComfyUIWorkflow = {
            id: workflow?.id || '',
            name: data.name,
            display_name: data.display_name,
            description: data.description || null,
            workflow_type: data.workflow_type,
            version: data.version || null,
            is_public: data.is_public,
            workflow_json: data.workflow_json as any,
            tags,
            author_id: workflow?.author_id || null,
            created_at: workflow?.created_at || new Date().toISOString(),
            updated_at: new Date().toISOString(),
        };

        onSave(workflowData);
    };

    const renderActivePane = () => {
        const currentPane = (() => {
            switch (activeTab) {
                case 'general':
                    return <WorkflowGeneralPane tags={tags} setTags={setTags} />;
                case 'json':
                    return <WorkflowJsonPane />;
                default:
                    return null;
            }
        })();

        return (
            <AnimatePresence mode="wait">
                <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.2 }}
                >
                    {currentPane}
                </motion.div>
            </AnimatePresence>
        );
    };

    return (
        <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex gap-6">
                <div className="w-[240px] border-r pr-4 sticky top-4">
                    <WorkflowEditorSidebar
                        activeTab={activeTab}
                        setActiveTab={setActiveTab}
                        onCancel={onCancel}
                        isPending={isPending}
                        isEditing={isEditing}
                    />
                </div>
                <div className="flex-1 h-[calc(100vh-280px)] overflow-y-auto pr-2 custom-scrollbar">
                    {renderActivePane()}
                </div>
            </form>
        </FormProvider>
    );
} 