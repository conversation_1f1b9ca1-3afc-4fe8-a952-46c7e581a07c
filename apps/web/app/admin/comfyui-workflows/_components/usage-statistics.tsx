'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { But<PERSON> } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import { ArrowLeft, BarChart3, Clock, Zap, TrendingUp, Users, DollarSign } from 'lucide-react';
import type { ComfyUIConfigUsage, ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';

interface UsageStatisticsProps {
    stats: ComfyUIConfigUsage[];
    configs: ComfyUIWorkflowConfigWithWorkflow[];
    onClose: () => void;
}

export function UsageStatistics({ stats, configs, onClose }: UsageStatisticsProps) {
    // 计算总体统计
    const totalGenerations = stats.reduce((sum, stat) => sum + stat.generation_count, 0);
    const totalCost = stats.reduce((sum, stat) => sum + (stat.total_cost || 0), 0);
    const totalProcessingTime = stats.reduce((sum, stat) => sum + (stat.total_processing_time_seconds || 0), 0);
    const uniqueUsers = new Set(stats.map(stat => stat.user_id)).size;

    // 按配置排序统计
    const statsByConfig = stats.sort((a, b) => b.generation_count - a.generation_count);

    // 最受欢迎的配置
    const topConfigs = statsByConfig.slice(0, 5);

    const formatDuration = (seconds: number) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        }
        return `${minutes}分钟`;
    };

    const getConfigName = (configId: string) => {
        const config = configs.find(c => c.id === configId);
        return config?.display_name || '未知配置';
    };

    const getWorkflowName = (configId: string) => {
        const config = configs.find(c => c.id === configId);
        return config?.workflow.display_name || '未知工作流';
    };

    return (
        <div className="space-y-6">
            {/* 标题栏 */}
            <div className="flex items-center gap-4">
                <Button variant="outline" onClick={onClose} className="gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    返回
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">使用统计</h1>
                    <p className="text-muted-foreground">ComfyUI 工作流配置的使用分析</p>
                </div>
            </div>

            {/* 总体统计卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">总生成次数</CardTitle>
                        <Zap className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{totalGenerations.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            累计图像生成数量
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{uniqueUsers}</div>
                        <p className="text-xs text-muted-foreground">
                            使用过工作流的用户数
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">总处理时间</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatDuration(totalProcessingTime)}</div>
                        <p className="text-xs text-muted-foreground">
                            累计GPU计算时间
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">总费用</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">${totalCost.toFixed(2)}</div>
                        <p className="text-xs text-muted-foreground">
                            累计生成费用
                        </p>
                    </CardContent>
                </Card>
            </div>

            <Tabs defaultValue="configs" className="space-y-4">
                <TabsList>
                    <TabsTrigger value="configs" className="gap-2">
                        <BarChart3 className="h-4 w-4" />
                        配置统计
                    </TabsTrigger>
                    <TabsTrigger value="trends" className="gap-2">
                        <TrendingUp className="h-4 w-4" />
                        使用趋势
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="configs" className="space-y-4">
                    {/* 最受欢迎的配置 */}
                    <Card>
                        <CardHeader>
                            <CardTitle>最受欢迎的配置</CardTitle>
                            <CardDescription>按生成次数排序的前5个配置</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {topConfigs.map((stat, index) => (
                                    <div key={stat.config_id} className="flex items-center justify-between p-3 rounded-lg border">
                                        <div className="flex items-center gap-3">
                                            <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                                                {index + 1}
                                            </Badge>
                                            <div>
                                                <h4 className="font-medium">{getConfigName(stat.config_id)}</h4>
                                                <p className="text-sm text-muted-foreground">{getWorkflowName(stat.config_id)}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-semibold">{stat.generation_count}次</p>
                                            <p className="text-xs text-muted-foreground">
                                                ${(stat.total_cost || 0).toFixed(2)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* 详细统计表格 */}
                    <Card>
                        <CardHeader>
                            <CardTitle>详细统计</CardTitle>
                            <CardDescription>所有配置的使用详情</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {statsByConfig.map((stat) => (
                                    <div key={stat.config_id} className="grid grid-cols-5 gap-4 p-3 rounded-lg border">
                                        <div>
                                            <h4 className="font-medium text-sm">{getConfigName(stat.config_id)}</h4>
                                            <p className="text-xs text-muted-foreground">{getWorkflowName(stat.config_id)}</p>
                                        </div>
                                        <div className="text-center">
                                            <p className="font-semibold">{stat.generation_count}</p>
                                            <p className="text-xs text-muted-foreground">生成次数</p>
                                        </div>
                                        <div className="text-center">
                                            <p className="font-semibold">{formatDuration(stat.total_processing_time_seconds || 0)}</p>
                                            <p className="text-xs text-muted-foreground">处理时间</p>
                                        </div>
                                        <div className="text-center">
                                            <p className="font-semibold">${(stat.total_cost || 0).toFixed(2)}</p>
                                            <p className="text-xs text-muted-foreground">总费用</p>
                                        </div>
                                        <div className="text-center">
                                            <p className="font-semibold">
                                                {stat.total_processing_time_seconds && stat.generation_count
                                                    ? Math.round((stat.total_processing_time_seconds / stat.generation_count) * 10) / 10
                                                    : 0}s
                                            </p>
                                            <p className="text-xs text-muted-foreground">平均时间</p>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {statsByConfig.length === 0 && (
                                <div className="text-center py-8">
                                    <p className="text-muted-foreground">暂无使用统计数据</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="trends" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle>使用趋势分析</CardTitle>
                            <CardDescription>工作流配置的使用模式和趋势</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                {/* 平均处理时间分析 */}
                                <div>
                                    <h4 className="font-medium mb-3">平均处理时间排名</h4>
                                    <div className="space-y-2">
                                        {statsByConfig
                                            .filter(stat => stat.generation_count > 0)
                                            .sort((a, b) => {
                                                const avgA = (a.total_processing_time_seconds || 0) / a.generation_count;
                                                const avgB = (b.total_processing_time_seconds || 0) / b.generation_count;
                                                return avgA - avgB;
                                            })
                                            .slice(0, 5)
                                            .map((stat) => {
                                                const avgTime = (stat.total_processing_time_seconds || 0) / stat.generation_count;
                                                return (
                                                    <div key={stat.config_id} className="flex items-center justify-between p-2 rounded border">
                                                        <span className="text-sm">{getConfigName(stat.config_id)}</span>
                                                        <Badge variant="outline">{Math.round(avgTime * 10) / 10}秒</Badge>
                                                    </div>
                                                );
                                            })}
                                    </div>
                                </div>

                                {/* 费用效率分析 */}
                                <div>
                                    <h4 className="font-medium mb-3">费用效率排名</h4>
                                    <div className="space-y-2">
                                        {statsByConfig
                                            .filter(stat => stat.generation_count > 0 && stat.total_cost && stat.total_cost > 0)
                                            .sort((a, b) => {
                                                const costPerGenA = (a.total_cost || 0) / a.generation_count;
                                                const costPerGenB = (b.total_cost || 0) / b.generation_count;
                                                return costPerGenA - costPerGenB;
                                            })
                                            .slice(0, 5)
                                            .map((stat) => {
                                                const costPerGen = (stat.total_cost || 0) / stat.generation_count;
                                                return (
                                                    <div key={stat.config_id} className="flex items-center justify-between p-2 rounded border">
                                                        <span className="text-sm">{getConfigName(stat.config_id)}</span>
                                                        <Badge variant="outline">${costPerGen.toFixed(3)}/次</Badge>
                                                    </div>
                                                );
                                            })}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
} 