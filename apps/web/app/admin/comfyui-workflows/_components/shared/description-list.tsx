'use client';

import React from 'react';

interface DescriptionListProps {
    children: React.ReactNode;
    title?: string;
    grid?: boolean;
}

export function DescriptionList({ children, title, grid = true }: DescriptionListProps) {
    return (
        <div>
            {title && <h3 className="text-lg font-medium mb-4">{title}</h3>}
            <dl className={grid ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4" : "space-y-4"}>
                {children}
            </dl>
        </div>
    );
}

interface DescriptionItemProps {
    label: string;
    children: React.ReactNode;
}

export function DescriptionItem({ label, children }: DescriptionItemProps) {
    return (
        <div className="flex flex-col">
            <dt className="text-sm font-medium text-muted-foreground">{label}</dt>
            <dd className="mt-1 text-sm text-foreground">{children}</dd>
        </div>
    );
}
