'use client';

import { useState } from 'react';
import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Input } from '@kit/ui/input';
import { Separator } from '@kit/ui/separator';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Search, Code2, Zap, FileText, Hash } from 'lucide-react';

export interface WorkflowNode {
  id: string;
  type: string;
  title: string;
  inputs: string[];
}

interface WorkflowNodeBrowserProps {
  workflowJson?: any;
  onNodePathClick?: (nodeId: string, inputName: string, fullPath: string) => void;
  className?: string;
  title?: string;
  description?: string;
  showDescription?: boolean;
}

export function WorkflowNodeBrowser({
  workflowJson,
  onNodePathClick,
  className,
}: WorkflowNodeBrowserProps) {
  const [searchTerm, setSearchTerm] = useState('');

  // 获取工作流中的节点信息
  const getWorkflowNodes = (): WorkflowNode[] => {
    if (!workflowJson) return [];
    return Object.entries(workflowJson)
      .filter(([_, node]: [string, any]) => node.class_type)
      .map(([id, node]: [string, any]) => ({
        id,
        type: node.class_type,
        title: node._meta?.title || node.class_type,
        inputs: Object.keys(node.inputs || {})
      }))
      .sort((a, b) => parseInt(a.id) - parseInt(b.id));
  };

  // 处理节点路径点击
  const handleNodePathClick = (nodeId: string, inputName: string) => {
    const fullPath = `${nodeId}.inputs.${inputName}`;
    if (onNodePathClick) {
      onNodePathClick(nodeId, inputName, fullPath);
    }
  };

  const nodes = getWorkflowNodes();
  
  // 过滤节点
  const filteredNodes = nodes.filter(node => 
    node.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    node.inputs.some(input => input.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (!workflowJson || nodes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
          <Code2 className="h-8 w-8 text-muted-foreground" />
        </div>
        <div className="mt-4">
          <h3 className="text-lg font-semibold">未选择工作流</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            请先在基础配置中选择一个工作流
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜索栏 */}
      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="搜索节点或参数..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* 统计信息 */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Hash className="h-4 w-4" />
          <span>{filteredNodes.length} / {nodes.length} 个节点</span>
        </div>
        <div className="flex items-center gap-1">
          <Zap className="h-4 w-4" />
          <span>{filteredNodes.reduce((sum, node) => sum + node.inputs.length, 0)} 个参数</span>
        </div>
      </div>

      <Separator />

      {/* 节点列表 */}
      <ScrollArea className="h-[400px]">
        <div className="space-y-3 pr-4">
          {filteredNodes.length === 0 ? (
            <div className="text-center py-8">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <Search className="h-6 w-6 text-muted-foreground" />
              </div>
              <div className="mt-3">
                <p className="text-sm font-medium">未找到匹配的节点</p>
                <p className="text-xs text-muted-foreground mt-1">尝试调整搜索关键词</p>
              </div>
            </div>
          ) : (
            filteredNodes.map((node) => (
              <Card key={node.id} className="hover:shadow-sm transition-shadow border-l-4 border-l-primary/20">
                <CardContent className="p-4">
                  {/* 节点头部信息 */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          #{node.id}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {node.type}
                        </Badge>
                      </div>
                      {node.title !== node.type && (
                        <h4 className="font-medium text-sm text-foreground">
                          {node.title}
                        </h4>
                      )}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <FileText className="h-3 w-3" />
                      <span>{node.inputs.length}</span>
                    </div>
                  </div>

                  {/* 输入参数 */}
                  {node.inputs.length > 0 ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-xs font-medium text-muted-foreground">输入参数:</span>
                        <span className="text-xs text-muted-foreground">点击添加为参数</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {node.inputs.map((input) => (
                          <Button
                            key={input}
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleNodePathClick(node.id, input)}
                            className="h-8 px-3 text-xs font-medium bg-gradient-to-r from-background to-accent/20 hover:from-primary hover:to-primary hover:text-primary-foreground transition-all duration-200 border-dashed hover:border-solid"
                          >
                            <Code2 className="h-3 w-3 mr-1" />
                            {input}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <p className="text-xs text-muted-foreground">此节点无输入参数</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );
}

// 导出节点类型供其他组件使用  
export type { WorkflowNode as BrowserWorkflowNode };