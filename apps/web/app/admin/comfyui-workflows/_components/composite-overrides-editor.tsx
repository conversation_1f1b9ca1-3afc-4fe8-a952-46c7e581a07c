'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Badge } from '@kit/ui/badge';
import {
  Plus,
  Trash2,
  ArrowRight,
  X,
} from 'lucide-react';
import { cn } from '@kit/ui/utils';
import { toast } from '@kit/ui/sonner';
import { WorkflowNodeBrowser } from './shared/workflow-node-browser';

interface CompositeOverrideValue {
  [nodePath: string]: any;
}

interface CompositeOverrideOption {
  overrides: CompositeOverrideValue;
  isDefault?: boolean;
}

interface CompositeOverrideMapping {
  [parameterValue: string]: CompositeOverrideOption;
}

interface CompositeOverride {
  [parameterName: string]: CompositeOverrideMapping;
}

interface CompositeOverridesEditorProps {
  value: CompositeOverride;
  onChange: (value: CompositeOverride) => void;
  workflowJson?: any;
  className?: string;
}

// 预设的参数示例
const PRESET_PARAMETERS = {
  aspect_ratio: {
    name: '纵横比',
    values: {
      '1:1': {
        name: '正方形 (1:1)',
        example: {
          overrides: { '5.inputs.width': 1024, '5.inputs.height': 1024 },
          isDefault: true
        }
      },
      '16:9': {
        name: '横屏 (16:9)',
        example: {
          overrides: { '5.inputs.width': 1344, '5.inputs.height': 768 },
          isDefault: false
        }
      },
      '9:16': {
        name: '竖屏 (9:16)',
        example: {
          overrides: { '5.inputs.width': 768, '5.inputs.height': 1344 },
          isDefault: false
        }
      },
      '4:3': {
        name: '标准 (4:3)',
        example: {
          overrides: { '5.inputs.width': 1152, '5.inputs.height': 896 },
          isDefault: false
        }
      },
      '3:4': {
        name: '竖版 (3:4)',
        example: {
          overrides: { '5.inputs.width': 896, '5.inputs.height': 1152 },
          isDefault: false
        }
      },
    }
  },
  quality_preset: {
    name: '质量预设',
    values: {
      'draft': {
        name: '草稿',
        example: {
          overrides: { '3.inputs.steps': 15, '3.inputs.cfg': 6 },
          isDefault: false
        }
      },
      'standard': {
        name: '标准',
        example: {
          overrides: { '3.inputs.steps': 25, '3.inputs.cfg': 7 },
          isDefault: true
        }
      },
      'high': {
        name: '高质量',
        example: {
          overrides: { '3.inputs.steps': 40, '3.inputs.cfg': 8 },
          isDefault: false
        }
      },
      'ultra': {
        name: '超高质量',
        example: {
          overrides: { '3.inputs.steps': 60, '3.inputs.cfg': 8.5 },
          isDefault: false
        }
      },
    }
  },
  style_preset: {
    name: '风格预设',
    values: {
      'photorealistic': {
        name: '写实',
        example: {
          overrides: { '3.inputs.cfg': 7 },
          isDefault: true
        }
      },
      'anime': {
        name: '动漫',
        example: {
          overrides: { '3.inputs.cfg': 9 },
          isDefault: false
        }
      },
      'digital_art': {
        name: '数字艺术',
        example: {
          overrides: { '3.inputs.cfg': 8 },
          isDefault: false
        }
      },
    }
  }
};

export default function CompositeOverridesEditor({
  value = {},
  onChange,
  workflowJson,
  className,
}: CompositeOverridesEditorProps) {
  const [selectedParameter, setSelectedParameter] = useState<string | null>(null);
  const [selectedValue, setSelectedValue] = useState<string | null>(null);
  const [newPath, setNewPath] = useState('');
  const [newValue, setNewValue] = useState('');
  const [isAddingParameter, setIsAddingParameter] = useState(false);
  const [newParameterName, setNewParameterName] = useState('');
  const [newParameterValue, setNewParameterValue] = useState('');

  // 添加新的组合参数
  const addParameter = () => {
    if (!newParameterName) {
      toast.error('请输入参数名称');
      return;
    }
    if (!newParameterValue) {
      toast.error('请输入参数值');
      return;
    }

    const updated = { ...value };
    if (!updated[newParameterName]) {
      updated[newParameterName] = {};
    }
    if (!updated[newParameterName][newParameterValue]) {
      updated[newParameterName][newParameterValue] = {
        overrides: {},
        isDefault: false
      };
    }

    onChange(updated);
    setSelectedParameter(newParameterName);
    setSelectedValue(newParameterValue);
    setIsAddingParameter(false);
    setNewParameterName('');
    setNewParameterValue('');
    toast.success('参数添加成功');
  };

  // 添加新的参数值
  const addParameterValue = (paramName: string, valueName: string) => {
    const updated = { ...value };
    if (!updated[paramName]) {
      updated[paramName] = {};
    }
    updated[paramName][valueName] = {
      overrides: {},
      isDefault: false
    };
    onChange(updated);
    setSelectedValue(valueName);
    toast.success('参数值添加成功');
  };

  // 添加覆写路径
  const addOverridePath = () => {
    if (!selectedParameter || !selectedValue) return;
    if (!newPath || !newValue) {
      toast.error('请输入完整的路径和值');
      return;
    }

    const updated = { ...value };
    if (!updated[selectedParameter]) {
      updated[selectedParameter] = {};
    }
    if (!updated[selectedParameter][selectedValue]) {
      updated[selectedParameter][selectedValue] = {
        overrides: {},
        isDefault: false
      };
    }

    // 尝试解析值
    let parsedValue: any = newValue;
    try {
      // 如果是数字或布尔值，进行转换
      if (/^\d+(\.\d+)?$/.test(newValue)) {
        parsedValue = parseFloat(newValue);
      } else if (newValue === 'true' || newValue === 'false') {
        parsedValue = newValue === 'true';
      } else if (newValue.startsWith('{') || newValue.startsWith('[')) {
        parsedValue = JSON.parse(newValue);
      }
    } catch (e) {
      // 保持字符串格式
    }

    updated[selectedParameter][selectedValue].overrides[newPath] = parsedValue;
    onChange(updated);
    setNewPath('');
    setNewValue('');
    toast.success('覆写路径添加成功');
  };

  // 删除覆写路径
  const deleteOverridePath = (paramName: string, valueName: string, path: string) => {
    const updated = { ...value };
    if (updated[paramName]?.[valueName]?.overrides) {
      delete updated[paramName][valueName].overrides[path];
      onChange(updated);
      toast.success('覆写路径已删除');
    }
  };

  // 删除参数值
  const deleteParameterValue = (paramName: string, valueName: string) => {
    const updated = { ...value };
    if (updated[paramName]) {
      delete updated[paramName][valueName];
      if (Object.keys(updated[paramName]).length === 0) {
        delete updated[paramName];
      }
      onChange(updated);
      if (selectedValue === valueName) {
        setSelectedValue(null);
      }
      toast.success('参数值已删除');
    }
  };

  // 删除参数
  const deleteParameter = (paramName: string) => {
    const updated = { ...value };
    delete updated[paramName];
    onChange(updated);
    if (selectedParameter === paramName) {
      setSelectedParameter(null);
      setSelectedValue(null);
    }
    toast.success('参数已删除');
  };

  // 设置/取消默认值
  const toggleDefault = (paramName: string, valueName: string) => {
    const updated = { ...value };
    if (!updated[paramName]?.[valueName]) return;

    // 先取消该参数下所有其他值的默认状态
    Object.keys(updated[paramName]).forEach(key => {
      const option = updated[paramName]?.[key];
      if (option && typeof option === 'object' && 'isDefault' in option) {
        option.isDefault = false;
      }
    });

    // 设置当前值为默认
    const currentOption = updated[paramName]?.[valueName];
    if (currentOption && typeof currentOption === 'object' && 'isDefault' in currentOption) {
      currentOption.isDefault = !currentOption.isDefault;

      onChange(updated);
      toast.success(
        currentOption.isDefault
          ? `已设置 "${valueName}" 为默认值`
          : `已取消 "${valueName}" 的默认状态`
      );
    }
  };

  // 从预设中导入
  const importFromPreset = (presetKey: string) => {
    const preset = PRESET_PARAMETERS[presetKey as keyof typeof PRESET_PARAMETERS];
    if (!preset) return;

    const updated = { ...value };
    updated[presetKey] = {};

    Object.entries(preset.values).forEach(([valueKey, valueData]) => {
      if ('example' in valueData && valueData.example) {
        if (!updated[presetKey]) {
          updated[presetKey] = {};
        }
        updated[presetKey][valueKey] = valueData.example;
      }
    });

    onChange(updated);
    setSelectedParameter(presetKey);
    toast.success(`已导入 ${preset.name} 预设`);
  };

  // 自动填入节点路径
  const fillNodePath = (nodeId: string, inputName: string) => {
    const path = `${nodeId}.inputs.${inputName}`;
    setNewPath(path);
    toast.success(`已填入路径: ${path}`);
  };

  // 处理工作流节点浏览器的路径点击
  const handleNodePathClick = (nodeId: string, inputName: string) => {
    fillNodePath(nodeId, inputName);
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle>组合覆写配置</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 工具栏 */}
        <div className="flex items-center gap-2">
          <Dialog open={isAddingParameter} onOpenChange={setIsAddingParameter}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                添加参数
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>添加组合参数</DialogTitle>
                <DialogDescription>
                  创建一个新的组合参数，可以通过单个参数控制多个节点值
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>参数名称</Label>
                  <Input
                    value={newParameterName}
                    onChange={(e) => setNewParameterName(e.target.value)}
                    placeholder="例如: aspect_ratio"
                  />
                </div>
                <div>
                  <Label>初始参数值</Label>
                  <Input
                    value={newParameterValue}
                    onChange={(e) => setNewParameterValue(e.target.value)}
                    placeholder="例如: 16:9"
                  />
                </div>
                <Button onClick={addParameter} className="w-full">
                  添加参数
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Select onValueChange={importFromPreset}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="从预设导入" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(PRESET_PARAMETERS).map(([key, preset]) => (
                <SelectItem key={key} value={key}>
                  {preset.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 主内容区 */}
        <div className="grid grid-cols-12 gap-4">
          {/* 参数列表 */}
          <div className="col-span-3 space-y-2">
            <h4 className="text-sm font-medium mb-2">参数列表</h4>
            <AnimatePresence>
              {Object.keys(value).map((paramName) => (
                <motion.div
                  key={paramName}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                >
                  <div
                    className={cn(
                      'p-2 rounded-md border cursor-pointer hover:bg-accent transition-colors',
                      selectedParameter === paramName && 'bg-accent border-primary'
                    )}
                    onClick={() => {
                      setSelectedParameter(paramName);
                      setSelectedValue(null);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{paramName}</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteParameter(paramName);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {Object.keys(value[paramName] || {}).length} 个值
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* 参数值列表 */}
          {selectedParameter && (
            <div className="col-span-3 space-y-2">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">参数值</h4>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>添加参数值</DialogTitle>
                    </DialogHeader>
                    <Input
                      placeholder="输入新的参数值"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.currentTarget.value) {
                          addParameterValue(selectedParameter, e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                  </DialogContent>
                </Dialog>
              </div>
              <AnimatePresence>
                {Object.keys(value[selectedParameter] ?? {}).map((valueName) => (
                  <motion.div
                    key={valueName}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                  >
                    <div
                      className={cn(
                        'p-2 rounded-md border cursor-pointer hover:bg-accent transition-colors',
                        selectedValue === valueName && 'bg-accent border-primary'
                      )}
                      onClick={() => setSelectedValue(valueName)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{valueName}</span>
                          {value[selectedParameter]?.[valueName]?.isDefault && (
                            <Badge variant="default" className="text-xs">默认</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant={value[selectedParameter]?.[valueName]?.isDefault ? "default" : "outline"}
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleDefault(selectedParameter, valueName);
                            }}
                            className="h-6 px-2 text-xs"
                          >
                            {value[selectedParameter]?.[valueName]?.isDefault ? "取消默认" : "设为默认"}
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteParameterValue(selectedParameter, valueName);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {Object.keys(value[selectedParameter]?.[valueName]?.overrides || {}).length} 个覆写
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}

          {/* 覆写配置 */}
          {selectedParameter && selectedValue && (
            <div className="col-span-6 space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  覆写配置: {selectedParameter} = {selectedValue}
                </h4>
              </div>

              {/* 添加新覆写 */}
              <div className="p-3 border rounded-md space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">节点路径</Label>
                    <Input
                      placeholder="例如: 5.inputs.width"
                      value={newPath}
                      onChange={(e) => setNewPath(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label className="text-xs">值</Label>
                    <Input
                      placeholder="例如: 1024"
                      value={newValue}
                      onChange={(e) => setNewValue(e.target.value)}
                    />
                  </div>
                </div>
                <Button size="sm" onClick={addOverridePath} className="w-full" type='button'>
                  添加覆写
                </Button>
              </div>

              {/* 现有覆写列表 */}
              <div className="space-y-2">
                <AnimatePresence>
                  {Object.entries(value[selectedParameter]?.[selectedValue]?.overrides ?? {}).map(([path, val]) => (
                    <motion.div
                      key={path}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="flex items-center gap-2 p-2 border rounded-md"
                    >
                      <code className="text-sm flex-1">{path}</code>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      <Badge variant="secondary">
                        {typeof val === 'object' ? JSON.stringify(val) : String(val)}
                      </Badge>
                      <Button
                        size="sm"
                        variant="ghost"
                        type="button"
                        onClick={() => deleteOverridePath(selectedParameter, selectedValue, path)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>

              {/* 工作流节点提示 */}
              {workflowJson && (
                <WorkflowNodeBrowser
                  workflowJson={workflowJson}
                  onNodePathClick={handleNodePathClick}
                  title="可用节点"
                  description="点击 input 自动填入路径"
                  className="mt-4"
                />
              )}
            </div>
          )}
        </div>

        {/* JSON 预览 */}
        <div className="mt-4">
          <details>
            <summary className="cursor-pointer text-sm font-medium mb-2">
              JSON 预览
            </summary>
            <pre className="p-2 bg-muted rounded-md text-xs overflow-x-auto">
              {JSON.stringify(value, null, 2)}
            </pre>
          </details>
        </div>
      </CardContent>
    </Card>
  );
}