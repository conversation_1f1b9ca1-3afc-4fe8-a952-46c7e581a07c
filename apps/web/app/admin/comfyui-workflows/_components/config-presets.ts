// ComfyUI 配置预设
// 提供基于工作流分析的智能配置生成

import { ComfyUIWorkflow } from '@kit/comfyui/types';
import { ParameterDefinition } from './editor/config-parameters-pane';
import { analyzeWorkflowForParameters } from '../_lib/analyze-workflow';

export type ConfigPreset = {
  id: string;
  name: string;
  description: string;
  workflowType: 'all' | 'txt2img' | 'img2img' | 'sdxl';
  icon: string;
  parameterMapping?: Record<string, string>;
  defaultParameters?: Record<string, any>;
  analyzeWorkflow?: (workflowJson: any) => ParameterDefinition[];
};

// Re-implementing smart analysis for SDXL preset
export const SDXL_BASE_PRESET: ConfigPreset = {
  id: 'SDXL_BASE_PRESET',
  name: 'SDXL 基础配置 (智能分析)',
  description: '通过智能分析，自动寻找适用于 Stable Diffusion XL 基础模型的标准参数。',
  workflowType: 'sdxl',
  icon: '🎨',
  defaultParameters: {
    "sampler_name": "euler",
    "scheduler": "normal",
    "denoise": 1,
    "width": 1024,
    "height": 1024,
    "batch_size": 1,
    "seed": -1,
    "steps": 20,
    "cfg": 7,
    "negative_prompt": "blurry, low quality",
    "prompt": "",
  },
  analyzeWorkflow: analyzeWorkflowForParameters,
};

export const SD15_PRESET: ConfigPreset = {
  id: 'SD15_PRESET',
  name: 'SD 1.5 经典配置',
  description: '适用于 Stable Diffusion 1.5 的经典配置。',
  workflowType: 'txt2img',
  icon: '⚡',
  parameterMapping: {
    "positive_prompt": "6.inputs.text",
    "negative_prompt": "7.inputs.text",
    "seed": "3.inputs.seed",
    "steps": "3.inputs.steps",
    "cfg": "3.inputs.cfg",
    "sampler_name": "3.inputs.sampler_name",
    "scheduler": "3.inputs.scheduler",
    "denoise": "3.inputs.denoise",
  },
  defaultParameters: {
    "sampler_name": "dpmpp_2m",
    "scheduler": "karras",
    "denoise": 1,
    "seed": -1,
    "steps": 25,
    "cfg": 8,
    "negative_prompt": "nsfw, blurry, low quality"
  },
};

export const CONFIG_PRESETS: ConfigPreset[] = [
  SDXL_BASE_PRESET,
  SD15_PRESET,
];

// getPresetsByWorkflowType now just filters, the analysis is done on application
export function getPresetsByWorkflowType(
  workflowType: ComfyUIWorkflow['workflow_type'],
): ConfigPreset[] {
  const specificPresets = CONFIG_PRESETS.filter(p => p.workflowType === workflowType);
  const allPresets = CONFIG_PRESETS.filter(p => p.workflowType === 'all');
  return [...specificPresets, ...allPresets];
}

// applyPresetToWorkflow is now much smarter
export function applyPresetToWorkflow(preset: ConfigPreset, workflowJson: any): {
    parameters: ParameterDefinition[];
} {
    let parameters: ParameterDefinition[] = [];
    const { parameterMapping = {}, defaultParameters = {}, analyzeWorkflow } = preset;

    // 1. Run dynamic analysis if it exists
    if (analyzeWorkflow) {
        parameters = analyzeWorkflow(workflowJson);
    }

    // 2. Apply static mapping for parameters not found by analysis
    for (const [name, path] of Object.entries(parameterMapping)) {
        if (!parameters.some(p => p.name === name)) {
            parameters.push({
                key: `static_${name}_${Math.random()}`,
                name,
                path: path as string,
                defaultValue: '',
            });
        }
    }
    
    // 3. Apply default values to all found parameters
    for (const param of parameters) {
        if (defaultParameters[param.name] !== undefined) {
            param.defaultValue = defaultParameters[param.name];
        }
    }

    // 4. Add any default parameters that don't have a path (pure defaults)
    for (const [name, value] of Object.entries(defaultParameters)) {
        if (!parameters.some(p => p.name === name)) {
            parameters.push({
                key: `default_${name}_${Math.random()}`,
                name,
                path: '', // No path, it's just a default value
                defaultValue: value,
            });
        }
    }

    return { parameters };
}

// 获取预设的显示名称
export function getPresetDisplayName(preset: ConfigPreset): string {
  return `${preset.icon} ${preset.name}`;
}