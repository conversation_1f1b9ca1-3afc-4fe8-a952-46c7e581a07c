'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from '@kit/ui/sonner';
import { ComfyUIWorkflow, ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';

import { analyzeWorkflowForParameters } from '../_lib/analyze-workflow';

import { ConfigEditorSidebar, type EditorPane } from './editor/config-editor-sidebar';
import { ConfigGeneralPane } from './editor/config-general-pane';
import { ConfigParametersPane, type ParameterDefinition } from './editor/config-parameters-pane';
import { ConfigOutputsPane } from './editor/config-outputs-pane';
import CompositeOverridesEditor from './composite-overrides-editor';

// Data Transformation Layer for new unified parameter management
function combineParameters(config: ComfyUIWorkflowConfigWithWorkflow): ParameterDefinition[] {
    const mapping = (config.parameter_mapping || {}) as Record<string, string>;
    const defaults = (config.default_parameters || {}) as Record<string, any>;

    const combined = Object.keys(mapping).map(name => ({
        key: `param_${name}_${Math.random()}`,
        name,
        path: mapping[name] || '',
        defaultValue: defaults[name] ?? '',
    }));

    // Add defaults that might not be in mapping
    Object.keys(defaults).forEach(name => {
        if (!combined.some(p => p.name === name)) {
            combined.push({
                key: `param_${name}_${Math.random()}`,
                name,
                path: '', // No path if it was only a default
                defaultValue: defaults[name],
            });
        }
    });

    return combined;
}

function splitParameters(parameters: ParameterDefinition[]): {
    parameter_mapping: Record<string, string>;
    default_parameters: Record<string, any>;
} {
    const parameter_mapping: Record<string, string> = {};
    const default_parameters: Record<string, any> = {};

    parameters.forEach(p => {
        if (p.name) {
            if (p.path) {
                parameter_mapping[p.name] = p.path;
            }
            if (p.defaultValue !== undefined && p.defaultValue !== null && p.defaultValue !== '') {
                try {
                    // Attempt to parse numbers and booleans from string input
                    const val = typeof p.defaultValue === 'string' ? JSON.parse(p.defaultValue) : p.defaultValue;
                    default_parameters[p.name] = val;
                } catch (e) {
                    default_parameters[p.name] = p.defaultValue;
                }
            }
        }
    });

    return { parameter_mapping, default_parameters };
}

const formSchema = z.object({
    display_name: z.string().min(1, '显示名称不能为空'),
    config_name: z.string().min(1, '配置名称不能为空'),
    workflow_id: z.string().uuid('请选择一个有效的工作流'),
    description: z.string().optional(),
    status: z.enum(['active', 'inactive', 'deprecated']),
    is_public: z.boolean(),
    is_featured: z.boolean(),
    display_order: z.number().int(),
    estimated_time_seconds: z.number().int().nullable().optional(),
    gpu_memory_mb: z.number().int().nullable().optional(),
    output_keys: z.array(z.string()),
    output_mapping: z.record(z.string()),
});

type FormSchemaType = z.infer<typeof formSchema>;

interface ConfigEditorProps {
    config: ComfyUIWorkflowConfigWithWorkflow | null;
    workflows: ComfyUIWorkflow[];
    onSave: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
    onCancel: () => void;
    isPending?: boolean;
    prefilledWorkflowId?: string;
}

export function ConfigEditor({ config, workflows, onSave, onCancel, isPending, prefilledWorkflowId }: ConfigEditorProps) {
    const [activePane, setActivePane] = useState<EditorPane>('general');
    const [parameters, setParameters] = useState<ParameterDefinition[]>([]);
    const [compositeOverrides, setCompositeOverrides] = useState<any>(config?.composite_overrides || {});

    const form = useForm<FormSchemaType>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            display_name: config?.display_name || '',
            config_name: config?.config_name || '',
            workflow_id: config?.workflow_id || prefilledWorkflowId || '',
            description: config?.description || '',
            status: config?.status || 'active',
            is_public: config?.is_public ?? true,
            is_featured: config?.is_featured ?? false,
            display_order: config?.display_order || 0,
            estimated_time_seconds: config?.estimated_time_seconds || 120,
            gpu_memory_mb: config?.gpu_memory_mb || null,
            output_keys: config?.output_keys || [],
            output_mapping: (config?.output_mapping && typeof config.output_mapping === 'object' && !Array.isArray(config.output_mapping)) 
                ? config.output_mapping as Record<string, string> 
                : {},
        },
    });

    useEffect(() => {
        if (config) {
            form.reset({
                display_name: config.display_name || '',
                config_name: config.config_name || '',
                workflow_id: config.workflow_id || '',
                description: config.description || '',
                status: config.status || 'active',
                is_public: config.is_public ?? true,
                is_featured: config.is_featured ?? false,
                display_order: config.display_order || 0,
                estimated_time_seconds: config.estimated_time_seconds || 120,
                gpu_memory_mb: config.gpu_memory_mb || null,
                output_keys: config.output_keys || [],
                output_mapping: (config.output_mapping && typeof config.output_mapping === 'object' && !Array.isArray(config.output_mapping)) 
                    ? config.output_mapping as Record<string, string> 
                    : {},
            });
            setParameters(combineParameters(config));
            setCompositeOverrides(config.composite_overrides || {});
        }
    }, [config, form]);

    const selectedWorkflow = workflows.find(w => w.id === form.watch('workflow_id'));

    const handleAnalyze = useCallback(() => {
        if (!selectedWorkflow) {
            toast.error('请先选择一个工作流才能进行分析。');
            return;
        }
        try {
            const analyzedParams = analyzeWorkflowForParameters(selectedWorkflow.workflow_json);
            if (analyzedParams.length === 0) {
                toast.info('未能从此工作流中自动分析出任何参数。');
                return;
            }
            // Merge analyzed parameters with existing ones, avoiding duplicates by name
            setParameters(prev => {
                const existingNames = new Set(prev.map(p => p.name));
                const newParams = analyzedParams.filter(p => !existingNames.has(p.name));
                return [...prev, ...newParams];
            });
            toast.success(`智能分析完成！新增了 ${analyzedParams.length} 个参数。`);
        } catch (error) {
            console.error('智能分析失败:', error);
            toast.error(`智能分析失败: ${(error as Error).message}`);
        }
    }, [selectedWorkflow]);

    const handleSubmit = (data: FormSchemaType) => {
        if (!selectedWorkflow) {
            toast.error('请选择一个工作流');
            return;
        }

        const { parameter_mapping, default_parameters } = splitParameters(parameters);

        const savedConfig: ComfyUIWorkflowConfigWithWorkflow = {
            id: config?.id || '',
            created_at: config?.created_at || new Date().toISOString(),
            ...data,
            description: data.description || null,
            estimated_time_seconds: data.estimated_time_seconds ?? null,
            gpu_memory_mb: data.gpu_memory_mb ?? null,
            node_overrides: config?.node_overrides || {},
            composite_overrides: compositeOverrides,
            parameter_mapping,
            default_parameters,
            parameter_limits: config?.parameter_limits || {},
            required_parameters: config?.required_parameters || [],
            workflow: selectedWorkflow,
            updated_at: new Date().toISOString(),
        };

        onSave(savedConfig);
    };

    const renderActivePane = () => {
        const currentPane = (() => {
            switch (activePane) {
                case 'general':
                    return <ConfigGeneralPane workflows={workflows} />;
                case 'parameters':
                    return <ConfigParametersPane
                        parameters={parameters}
                        setParameters={setParameters}
                        onAnalyze={handleAnalyze}
                        canAnalyze={!!selectedWorkflow}
                        workflowJson={selectedWorkflow?.workflow_json}
                    />;
                case 'outputs':
                    return <ConfigOutputsPane workflowJson={selectedWorkflow?.workflow_json} />;
                case 'composite':
                    return <CompositeOverridesEditor
                        value={compositeOverrides}
                        onChange={setCompositeOverrides}
                        workflowJson={selectedWorkflow?.workflow_json}
                    />;
                default:
                    return <ConfigGeneralPane workflows={workflows} />;
            }
        })();

        return (
            <AnimatePresence mode="wait">
                <motion.div
                    key={activePane}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.2 }}
                >
                    {currentPane}
                </motion.div>
            </AnimatePresence>
        );
    };

    return (
        <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="flex gap-6">
                <div className="w-[240px] border-r pr-4 sticky top-4">
                    <ConfigEditorSidebar activePane={activePane} setActivePane={setActivePane} onCancel={onCancel} isPending={isPending} />
                </div>
                <div className="flex-1 h-[calc(100vh-280px)] overflow-y-auto pr-2 custom-scrollbar">
                    {renderActivePane()}
                </div>
            </form>
        </FormProvider>
    );
} 