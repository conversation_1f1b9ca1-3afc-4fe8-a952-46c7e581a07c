'use client';

import { Card, CardContent } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Cog, Trash2, Eye } from 'lucide-react';
import type { ComfyUIWorkflow, ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';

interface ConfigListProps {
    configs: ComfyUIWorkflowConfigWithWorkflow[];
    onViewConfig: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
    onEditConfig: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
    onDeleteConfig: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
}

export function ConfigList({
    configs,
    onViewConfig,
    onEditConfig,
    onDeleteConfig,
}: ConfigListProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge variant="default" className="bg-green-500 text-white">可用</Badge>;
            case 'inactive':
                return <Badge variant="secondary">不可用</Badge>;
            case 'deprecated':
                return <Badge variant="outline">已弃用</Badge>;
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    return (
        <div className="space-y-4">
            {configs.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                    <Cog className="mx-auto h-12 w-12" />
                    <h3 className="mt-4 text-lg font-medium">暂无配置</h3>
                    <p className="mt-1 text-sm">点击右上角的"创建配置"按钮开始。</p>
                </div>
            ) : (
                configs.map((config) => (
                    <Card key={config.id} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4 flex items-center justify-between">
                            <div className="flex-1 overflow-hidden">
                                <div className="flex items-center gap-3">
                                    <h3 className="font-semibold text-lg truncate" title={config.display_name}>
                                        {config.display_name}
                                    </h3>
                                    {getStatusBadge(config.status)}
                                    {config.is_featured && <Badge variant="default" className="bg-purple-500 text-white">精选</Badge>}
                                </div>
                                <p className="text-sm text-muted-foreground mt-1 truncate">
                                    工作流: {config.workflow.display_name}
                                </p>
                                <p className="text-xs text-muted-foreground mt-2 truncate">
                                    ID: <span className="font-mono">{config.id}</span>
                                </p>
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                                <Button variant="outline" size="sm" onClick={() => onViewConfig(config)}>
                                    <Eye className="h-4 w-4 mr-1" /> 查看
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => onEditConfig(config)}>
                                    <Cog className="h-4 w-4 mr-1" /> 编辑
                                </Button>
                                <Button variant="destructive" size="sm" onClick={() => onDeleteConfig(config)}>
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ))
            )}
        </div>
    );
} 