'use client';

import { useState, useMemo } from 'react';
import { useFormContext, useFieldArray } from 'react-hook-form';
import { Plus, Trash2, HelpCircle, Search, Download, Settings2, Filter, X, Target, FileText, Hash } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';
import { Badge } from '@kit/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Separator } from '@kit/ui/separator';
import { toast } from '@kit/ui/sonner';
import { WorkflowNodeBrowser } from '../shared/workflow-node-browser';

interface ConfigOutputsPaneProps {
    workflowJson?: any;
}

export function ConfigOutputsPane({ workflowJson }: ConfigOutputsPaneProps) {
    const { control, watch, setValue } = useFormContext();
    const [newOutputKey, setNewOutputKey] = useState('');
    const [newOutputNode, setNewOutputNode] = useState('');
    const [isNodeBrowserOpen, setIsNodeBrowserOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterBy, setFilterBy] = useState<'all' | 'hasMapping' | 'noMapping'>('all');
    const [editingKeys, setEditingKeys] = useState<Record<string, string>>({});

    // 观察当前的输出配置
    const outputKeys = watch('output_keys') || [];
    const outputMapping = watch('output_mapping') || {};

    // 开始编辑输出键
    const startEditingKey = (key: string) => {
        setEditingKeys(prev => ({ ...prev, [key]: key }));
    };

    // 处理输出键编辑中的变化
    const handleKeyChange = (originalKey: string, newValue: string) => {
        setEditingKeys(prev => ({ ...prev, [originalKey]: newValue }));
    };

    // 完成输出键编辑（失去焦点时）
    const finishEditingKey = (originalKey: string) => {
        const newKey = editingKeys[originalKey];
        if (newKey !== undefined) {
            const success = updateOutputKey(originalKey, newKey);
            if (success) {
                // 清除编辑状态
                setEditingKeys(prev => {
                    const updated = { ...prev };
                    delete updated[originalKey];
                    return updated;
                });
            } else {
                // 如果更新失败，恢复原值
                setEditingKeys(prev => ({ ...prev, [originalKey]: originalKey }));
            }
        }
    };

    // 过滤和搜索输出配置
    const filteredOutputs = useMemo(() => {
        let filtered = outputKeys;

        // 按过滤器筛选
        switch (filterBy) {
            case 'hasMapping':
                filtered = filtered.filter((key: string) => outputMapping[key] && outputMapping[key].trim() !== '');
                break;
            case 'noMapping':
                filtered = filtered.filter((key: string) => !outputMapping[key] || outputMapping[key].trim() === '');
                break;
            default:
                break;
        }

        // 按搜索词搜索
        if (searchTerm) {
            filtered = filtered.filter((key: string) => 
                key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (outputMapping[key] && outputMapping[key].toString().toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        return filtered;
    }, [outputKeys, outputMapping, searchTerm, filterBy]);

    const clearSearch = () => {
        setSearchTerm('');
        setFilterBy('all');
    };

    // 处理节点浏览器的节点点击 - 对于输出节点，我们只需要节点ID
    const handleNodeClick = (nodeId: string, inputName: string, fullPath: string) => {
        // 检查是否已经存在相同节点的输出配置
        const existingOutput = Object.entries(outputMapping).find(([key, value]) => value === nodeId);
        if (existingOutput) {
            toast.info(`节点 ${nodeId} 已存在于输出配置 "${existingOutput[0]}"`);
            return;
        }

        // 生成智能输出键名称 - 使用输入参数名作为键名，如果没有则使用node_节点ID
        const outputKey = inputName || `node_${nodeId}`;
        
        // 检查键名是否已存在
        if (outputKeys.includes(outputKey)) {
            toast.error(`输出键 "${outputKey}" 已存在`);
            return;
        }

        // 更新 output_keys 数组
        const updatedKeys = [...outputKeys, outputKey];
        setValue('output_keys', updatedKeys);

        // 更新 output_mapping 对象
        const updatedMapping = { ...outputMapping };
        updatedMapping[outputKey] = nodeId;
        setValue('output_mapping', updatedMapping);

        toast.success(`已添加输出配置 "${outputKey}" -> 节点 ${nodeId}`);
        
        // 创建成功后关闭模态框
        setIsNodeBrowserOpen(false);
    };

    // 添加新的输出配置
    const addOutput = () => {
        if (!newOutputKey.trim() || !newOutputNode.trim()) return;

        // 检查是否已存在
        if (outputKeys.includes(newOutputKey.trim())) {
            toast.error(`输出键 "${newOutputKey}" 已存在`);
            return;
        }

        // 更新 output_keys 数组
        const updatedKeys = [...outputKeys, newOutputKey.trim()];
        setValue('output_keys', updatedKeys);

        // 更新 output_mapping 对象
        const updatedMapping = { ...outputMapping };
        updatedMapping[newOutputKey.trim()] = newOutputNode.trim();
        setValue('output_mapping', updatedMapping);

        // 清空输入
        setNewOutputKey('');
        setNewOutputNode('');
        
        toast.success(`已添加输出配置 "${newOutputKey}" -> 节点 ${newOutputNode}`);
    };

    // 删除输出配置
    const removeOutput = (key: string) => {
        // 从 output_keys 数组中移除
        const updatedKeys = outputKeys.filter((k: string) => k !== key);
        setValue('output_keys', updatedKeys);

        // 从 output_mapping 对象中移除
        const updatedMapping = { ...outputMapping };
        delete updatedMapping[key];
        setValue('output_mapping', updatedMapping);
        
        toast.success(`已删除输出配置 "${key}"`);
    };

    // 更新输出节点映射
    const updateOutputMapping = (key: string, nodeId: string) => {
        const updatedMapping = { ...outputMapping };
        updatedMapping[key] = nodeId;
        setValue('output_mapping', updatedMapping);
    };

    // 更新输出键名称 - 失去焦点时保存
    const updateOutputKey = (oldKey: string, newKey: string) => {
        if (!newKey.trim()) {
            toast.error('输出键不能为空');
            return false;
        }
        
        // 如果没有变化，直接返回
        if (newKey.trim() === oldKey) {
            return true;
        }
        
        // 检查新键名是否已存在
        if (outputKeys.includes(newKey.trim())) {
            toast.error(`输出键 "${newKey}" 已存在`);
            return false;
        }

        // 更新 output_keys 数组
        const updatedKeys = outputKeys.map((k: string) => k === oldKey ? newKey.trim() : k);
        setValue('output_keys', updatedKeys);

        // 更新 output_mapping 对象
        const updatedMapping = { ...outputMapping };
        if (outputMapping[oldKey]) {
            updatedMapping[newKey.trim()] = outputMapping[oldKey];
            delete updatedMapping[oldKey];
        }
        setValue('output_mapping', updatedMapping);

        toast.success(`输出键已更新为 "${newKey}"`);
        return true;
    };

    const itemVariants = {
        hidden: { opacity: 0, scale: 0.95 },
        visible: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.95 },
    };

    return (
        <div className="space-y-6">
            {/* 顶部工具栏 */}
            <Card className="border-dashed bg-gradient-to-r from-background to-accent/10">
                <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-1">
                            <CardTitle className="text-2xl font-semibold tracking-tight flex items-center gap-2">
                                <Download className="h-6 w-6 text-primary" />
                                输出节点配置
                            </CardTitle>
                            <p className="text-sm text-muted-foreground">
                                配置工作流的输出节点映射，定义哪些节点的输出会被系统使用
                            </p>
                        </div>
                        <div className="flex items-center gap-3">
                            <Badge variant="secondary" className="gap-1 px-3 py-1">
                                <Hash className="h-3 w-3" />
                                {outputKeys.length} 个输出
                            </Badge>
                            <Badge variant="outline" className="gap-1 px-3 py-1">
                                <Target className="h-3 w-3" />
                                {filteredOutputs.length} 显示
                            </Badge>
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="space-y-4">
                    {/* 搜索和过滤器 */}
                    <div className="flex items-center gap-3">
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="搜索输出键或节点ID..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10 pr-10"
                            />
                            {searchTerm && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setSearchTerm('')}
                                    className="absolute right-1 top-1 h-7 w-7 p-0"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Filter className="h-4 w-4 text-muted-foreground" />
                            <select
                                value={filterBy}
                                onChange={(e) => setFilterBy(e.target.value as any)}
                                className="px-3 py-2 border rounded-md text-sm bg-background"
                            >
                                <option value="all">全部输出</option>
                                <option value="hasMapping">已配置</option>
                                <option value="noMapping">待配置</option>
                            </select>
                        </div>

                        {(searchTerm || filterBy !== 'all') && (
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={clearSearch}
                                className="gap-2"
                            >
                                <X className="h-4 w-4" />
                                清除
                            </Button>
                        )}
                    </div>

                    {/* 操作按钮 */}
                    <Separator />
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            {/* 节点浏览器模态框 */}
                            <Dialog open={isNodeBrowserOpen} onOpenChange={setIsNodeBrowserOpen}>
                                <DialogTrigger asChild>
                                    <Button
                                        type="button"
                                        variant="default"
                                        size="sm"
                                        disabled={!workflowJson}
                                        className="gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                                    >
                                        <Search className="h-4 w-4" />
                                        浏览节点
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                                    <DialogHeader>
                                        <DialogTitle>选择输出节点</DialogTitle>
                                        <DialogDescription>
                                            选择要设置为输出的节点。点击任意输入参数即可自动创建输出配置，参数名称将作为输出键名。
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="overflow-y-auto max-h-[60vh] pr-2">
                                        {workflowJson ? (
                                            <WorkflowNodeBrowser
                                                workflowJson={workflowJson}
                                                onNodePathClick={handleNodeClick}
                                                title=""
                                                description=""
                                                showDescription={false}
                                                className="border-0 shadow-none bg-transparent p-0"
                                            />
                                        ) : (
                                            <div className="text-center py-12">
                                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                                                    <Settings2 className="h-8 w-8 text-muted-foreground" />
                                                </div>
                                                <div className="mt-4">
                                                    <h3 className="text-lg font-semibold">未选择工作流</h3>
                                                    <p className="mt-2 text-sm text-muted-foreground">
                                                        请先在基础配置中选择一个工作流
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <Button 
                            type="button" 
                            variant="secondary" 
                            size="sm" 
                            onClick={() => {
                                // 创建快捷添加输出配置的逻辑可以放在这里
                                // 暂时保留原有的手动添加功能，会在后面的卡片中显示
                            }}
                            className="gap-2"
                        >
                            <Plus className="h-4 w-4" />
                            手动添加
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* 输出配置列表 */}
            <div className="space-y-4">
                {outputKeys.length === 0 ? (
                    <Card className="border-dashed bg-gradient-to-br from-background to-muted/20">
                        <CardContent className="flex flex-col items-center justify-center py-16">
                            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-muted to-muted/50">
                                <Download className="h-10 w-10 text-muted-foreground" />
                            </div>
                            <div className="mt-6 text-center">
                                <h3 className="text-lg font-semibold">还没有配置输出</h3>
                                <p className="mt-2 text-sm text-muted-foreground max-w-md">
                                    点击上方的"浏览节点"按钮从工作流中快速选择输出节点，或使用"手动添加"创建自定义输出配置
                                </p>
                                <div className="mt-6 flex items-center justify-center gap-3">
                                    <Dialog open={isNodeBrowserOpen} onOpenChange={setIsNodeBrowserOpen}>
                                        <DialogTrigger asChild>
                                            <Button 
                                                disabled={!workflowJson} 
                                                className="gap-2 bg-gradient-to-r from-primary to-primary/80"
                                            >
                                                <Search className="h-4 w-4" />
                                                浏览节点
                                            </Button>
                                        </DialogTrigger>
                                    </Dialog>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ) : (
                    <>
                        {/* 搜索结果统计 */}
                        {(searchTerm || filterBy !== 'all') && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <div className="flex items-center gap-2">
                                    <Search className="h-4 w-4" />
                                    <span>
                                        显示 {filteredOutputs.length} / {outputKeys.length} 个输出
                                        {searchTerm && ` (搜索: "${searchTerm}")`}
                                        {filterBy !== 'all' && ` (过滤: ${
                                            filterBy === 'hasMapping' ? '已配置' : '待配置'
                                        })`}
                                    </span>
                                </div>
                                {filteredOutputs.length === 0 && (
                                    <Badge variant="secondary">无匹配结果</Badge>
                                )}
                            </div>
                        )}

                        {/* 输出列表 */}
                        {filteredOutputs.length === 0 ? (
                            <Card className="border-dashed">
                                <CardContent className="flex flex-col items-center justify-center py-12">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                                        <Search className="h-8 w-8 text-muted-foreground" />
                                    </div>
                                    <div className="mt-4 text-center">
                                        <h3 className="text-lg font-semibold">未找到匹配的输出</h3>
                                        <p className="mt-2 text-sm text-muted-foreground">
                                            尝试调整搜索条件或清除过滤器
                                        </p>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={clearSearch}
                                            className="mt-4 gap-2"
                                        >
                                            <X className="h-4 w-4" />
                                            清除搜索
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ) : (
                            <div className="grid gap-4">
                                <AnimatePresence>
                                    {filteredOutputs.map((key: string) => (
                                        <motion.div
                                            key={key}
                                            layout
                                            variants={itemVariants}
                                            initial="hidden"
                                            animate="visible"
                                            exit="exit"
                                            transition={{ duration: 0.2 }}
                                        >
                                            <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-primary/20 hover:border-l-primary/50 bg-gradient-to-r from-background to-accent/5">
                                                <CardContent className="p-6">
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                        <div className="space-y-2">
                                                            <Label className="text-sm font-medium flex items-center gap-2">
                                                                <FileText className="h-4 w-4 text-primary" />
                                                                输出键
                                                            </Label>
                                                            <div className="relative">
                                                                <FileText className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                                                <Input
                                                                    value={editingKeys[key] !== undefined ? editingKeys[key] : key}
                                                                    onFocus={() => startEditingKey(key)}
                                                                    onChange={(e) => handleKeyChange(key, e.target.value)}
                                                                    onBlur={() => finishEditingKey(key)}
                                                                    onKeyDown={(e) => {
                                                                        if (e.key === 'Enter') {
                                                                            e.currentTarget.blur();
                                                                        }
                                                                        if (e.key === 'Escape') {
                                                                            setEditingKeys(prev => {
                                                                                const updated = { ...prev };
                                                                                delete updated[key];
                                                                                return updated;
                                                                            });
                                                                            e.currentTarget.blur();
                                                                        }
                                                                    }}
                                                                    placeholder="输入输出键名称"
                                                                    className="pl-10 font-medium"
                                                                />
                                                            </div>
                                                            <p className="text-xs text-muted-foreground">点击编辑，按 Enter 保存，按 Esc 取消</p>
                                                        </div>
                                                        
                                                        <div className="space-y-2">
                                                            <Label className="text-sm font-medium flex items-center gap-2">
                                                                <Target className="h-4 w-4 text-primary" />
                                                                节点ID
                                                            </Label>
                                                            <div className="relative">
                                                                <Hash className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                                                <Input
                                                                    value={outputMapping[key] || ''}
                                                                    onChange={(e) => updateOutputMapping(key, e.target.value)}
                                                                    placeholder="输入节点ID（如：9）"
                                                                    className="pl-10 font-mono text-sm"
                                                                />
                                                            </div>
                                                            <p className="text-xs text-muted-foreground">ComfyUI 工作流中的节点编号</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="flex items-center justify-between mt-6 pt-4 border-t">
                                                        <div className="flex items-center gap-2 flex-wrap">
                                                            <Badge variant="default" className="text-xs gap-1">
                                                                <FileText className="h-3 w-3" />
                                                                {key}
                                                            </Badge>
                                                            {outputMapping[key] && (
                                                                <Badge variant="secondary" className="text-xs font-mono gap-1">
                                                                    <Target className="h-3 w-3" />
                                                                    节点 {outputMapping[key]}
                                                                </Badge>
                                                            )}
                                                            {!outputMapping[key] && (
                                                                <Badge variant="destructive" className="text-xs gap-1">
                                                                    <X className="h-3 w-3" />
                                                                    待配置
                                                                </Badge>
                                                            )}
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeOutput(key)}
                                                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                                        >
                                                            <Trash2 className="h-4 w-4 mr-1" />
                                                            删除输出
                                                        </Button>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </motion.div>
                                    ))}
                                </AnimatePresence>
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* 手动添加输出配置 */}
            <Card>
                <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        手动添加输出配置
                    </CardTitle>
                    <CardDescription className="text-xs">
                        为工作流添加新的输出节点映射
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                        <div>
                            <Label htmlFor="output-key" className="text-xs flex items-center gap-2">
                                <FileText className="h-3 w-3" />
                                输出键 *
                            </Label>
                            <Input
                                id="output-key"
                                value={newOutputKey}
                                onChange={(e) => setNewOutputKey(e.target.value)}
                                placeholder="如：images"
                                className="mt-1 h-8"
                            />
                        </div>
                        <div>
                            <Label htmlFor="output-node" className="text-xs flex items-center gap-2">
                                <Target className="h-3 w-3" />
                                节点ID *
                            </Label>
                            <Input
                                id="output-node"
                                value={newOutputNode}
                                onChange={(e) => setNewOutputNode(e.target.value)}
                                placeholder="如：9"
                                className="mt-1 h-8"
                            />
                        </div>
                    </div>

                    <Button
                        type="button"
                        onClick={addOutput}
                        disabled={!newOutputKey.trim() || !newOutputNode.trim()}
                        className="w-full h-8 text-xs"
                        size="sm"
                    >
                        <Plus className="h-3 w-3 mr-1" />
                        添加输出配置
                    </Button>
                </CardContent>
            </Card>

            {/* 常用输出键提示 */}
            <Card className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
                <CardContent className="py-3">
                    <div className="flex items-start gap-2">
                        <HelpCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                        <div className="text-xs">
                            <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">常用输出键</p>
                            <div className="flex flex-wrap gap-1">
                                {['images', 'masks', 'latents', 'depth', 'controlnet'].map(key => (
                                    <Badge 
                                        key={key} 
                                        variant="outline" 
                                        className="text-xs cursor-pointer border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/50"
                                        onClick={() => setNewOutputKey(key)}
                                    >
                                        {key}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}