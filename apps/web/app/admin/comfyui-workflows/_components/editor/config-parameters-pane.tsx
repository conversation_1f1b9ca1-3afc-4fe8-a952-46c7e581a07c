'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Badge } from '@kit/ui/badge';
import { Separator } from '@kit/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Plus, Trash2, Wand2, Settings2, Code2, Hash, Search, Filter, X, FileText, Target } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import { WorkflowNodeBrowser } from '../shared/workflow-node-browser';

export type ParameterDefinition = {
    // A unique key for React list rendering, not for the database
    key: string;
    name: string;
    path: string;
    defaultValue: any;
};

interface ConfigParametersPaneProps {
    parameters: ParameterDefinition[];
    setParameters: (parameters: ParameterDefinition[] | ((prev: ParameterDefinition[]) => ParameterDefinition[])) => void;
    onAnalyze: () => void;
    canAnalyze: boolean;
    workflowJson?: any;
}

export function ConfigParametersPane({
    parameters,
    setParameters,
    onAnalyze,
    canAnalyze,
    workflowJson,
}: ConfigParametersPaneProps) {
    const [isNodeBrowserOpen, setIsNodeBrowserOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterBy, setFilterBy] = useState<'all' | 'hasPath' | 'hasDefault' | 'empty'>('all');

    // 过滤和搜索参数
    const filteredParameters = useMemo(() => {
        let filtered = parameters;

        // 按过滤器筛选
        switch (filterBy) {
            case 'hasPath':
                filtered = filtered.filter(p => p.path && p.path.trim() !== '');
                break;
            case 'hasDefault':
                filtered = filtered.filter(p => p.defaultValue && p.defaultValue.toString().trim() !== '');
                break;
            case 'empty':
                filtered = filtered.filter(p => !p.name || !p.path || !p.defaultValue);
                break;
            default:
                break;
        }

        // 按搜索词搜索
        if (searchTerm) {
            filtered = filtered.filter(p => 
                p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                p.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
                p.defaultValue.toString().toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        return filtered;
    }, [parameters, searchTerm, filterBy]);

    const clearSearch = () => {
        setSearchTerm('');
        setFilterBy('all');
    };

    const addParameter = () => {
        setParameters(prev => [
            {
                key: `param_${Date.now()}_${Math.random()}`,
                name: '',
                path: '',
                defaultValue: '',
            },
            ...prev,
        ]);
    };

    const updateParameter = (key: string, field: keyof Omit<ParameterDefinition, 'key'>, value: any) => {
        setParameters(prev =>
            prev.map((p) =>
                p.key === key ? { ...p, [field]: value } : p
            )
        );
    };

    const deleteParameter = (key: string) => {
        setParameters(prev => prev.filter((p) => p.key !== key));
    };

    // 智能创建参数
    const handleNodePathClick = (nodeId: string, inputName: string, fullPath: string) => {
        // 检查是否已经存在相同路径的参数
        const existingParam = parameters.find(p => p.path === fullPath);
        if (existingParam) {
            toast.info(`路径 ${fullPath} 已存在于参数 "${existingParam.name}"`);
            return;
        }

        // 生成智能参数名称 - 使用 inputName 作为参数名
        const paramName = inputName;
        
        // 创建新参数
        const newParam: ParameterDefinition = {
            key: `param_${Date.now()}_${Math.random()}`,
            name: paramName,
            path: fullPath,
            defaultValue: '',
        };

        setParameters(prev => [newParam, ...prev]);
        toast.success(`已创建参数 "${paramName}"，路径: ${fullPath}`);
        
        // 创建成功后关闭模态框
        setIsNodeBrowserOpen(false);
    };

    const itemVariants = {
        hidden: { opacity: 0, scale: 0.95 },
        visible: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.95 },
    };

    return (
        <div className="space-y-6">
            {/* 顶部工具栏 */}
            <Card className="border-dashed bg-gradient-to-r from-background to-accent/10">
                <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                        <div className="space-y-1">
                            <CardTitle className="text-2xl font-semibold tracking-tight flex items-center gap-2">
                                <Settings2 className="h-6 w-6 text-primary" />
                                输入参数配置
                            </CardTitle>
                            <p className="text-sm text-muted-foreground">
                                管理工作流的输入参数和默认值，支持智能分析和快速创建
                            </p>
                        </div>
                        <div className="flex items-center gap-3">
                            <Badge variant="secondary" className="gap-1 px-3 py-1">
                                <Hash className="h-3 w-3" />
                                {parameters.length} 个参数
                            </Badge>
                            <Badge variant="outline" className="gap-1 px-3 py-1">
                                <Target className="h-3 w-3" />
                                {filteredParameters.length} 显示
                            </Badge>
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="space-y-4">
                    {/* 搜索和过滤器 */}
                    <div className="flex items-center gap-3">
                        <div className="relative flex-1 max-w-md">
                            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="搜索参数名称、路径或默认值..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10 pr-10"
                            />
                            {searchTerm && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setSearchTerm('')}
                                    className="absolute right-1 top-1 h-7 w-7 p-0"
                                >
                                    <X className="h-3 w-3" />
                                </Button>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Filter className="h-4 w-4 text-muted-foreground" />
                            <select
                                value={filterBy}
                                onChange={(e) => setFilterBy(e.target.value as any)}
                                className="px-3 py-2 border rounded-md text-sm bg-background"
                            >
                                <option value="all">全部参数</option>
                                <option value="hasPath">有路径</option>
                                <option value="hasDefault">有默认值</option>
                                <option value="empty">待完善</option>
                            </select>
                        </div>

                        {(searchTerm || filterBy !== 'all') && (
                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={clearSearch}
                                className="gap-2"
                            >
                                <X className="h-4 w-4" />
                                清除
                            </Button>
                        )}
                    </div>

                    {/* 操作按钮 */}
                    <Separator />
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            {/* 节点浏览器模态框 */}
                            <Dialog open={isNodeBrowserOpen} onOpenChange={setIsNodeBrowserOpen}>
                                <DialogTrigger asChild>
                                    <Button
                                        type="button"
                                        variant="default"
                                        size="sm"
                                        disabled={!workflowJson}
                                        className="gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                                    >
                                        <Search className="h-4 w-4" />
                                        浏览节点
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                                    <DialogHeader>
                                        <DialogTitle>工作流节点浏览器</DialogTitle>
                                        <DialogDescription>
                                            选择要添加为参数的节点输入。点击任意输入参数即可自动创建对应的参数配置。
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="overflow-y-auto max-h-[60vh] pr-2">
                                        {workflowJson ? (
                                            <WorkflowNodeBrowser
                                                workflowJson={workflowJson}
                                                onNodePathClick={handleNodePathClick}
                                                title=""
                                                description=""
                                                showDescription={false}
                                                className="border-0 shadow-none bg-transparent p-0"
                                            />
                                        ) : (
                                            <div className="text-center py-12">
                                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                                                    <Code2 className="h-8 w-8 text-muted-foreground" />
                                                </div>
                                                <div className="mt-4">
                                                    <h3 className="text-lg font-semibold">未选择工作流</h3>
                                                    <p className="mt-2 text-sm text-muted-foreground">
                                                        请先在基础配置中选择一个工作流
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </DialogContent>
                            </Dialog>

                            <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={onAnalyze}
                                disabled={!canAnalyze}
                                className="gap-2"
                            >
                                <Wand2 className="h-4 w-4" />
                                智能分析
                            </Button>
                        </div>

                        <Button 
                            type="button" 
                            variant="secondary" 
                            size="sm" 
                            onClick={addParameter} 
                            className="gap-2"
                        >
                            <Plus className="h-4 w-4" />
                            手动添加
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* 参数列表区域 */}
            <div className="space-y-4">
                {parameters.length === 0 ? (
                    <Card className="border-dashed bg-gradient-to-br from-background to-muted/20">
                        <CardContent className="flex flex-col items-center justify-center py-16">
                            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-muted to-muted/50">
                                <FileText className="h-10 w-10 text-muted-foreground" />
                            </div>
                            <div className="mt-6 text-center">
                                <h3 className="text-lg font-semibold">还没有配置参数</h3>
                                <p className="mt-2 text-sm text-muted-foreground max-w-md">
                                    点击上方的"浏览节点"按钮从工作流中快速选择参数，或使用"手动添加"创建自定义参数
                                </p>
                                <div className="mt-6 flex items-center justify-center gap-3">
                                    <Dialog open={isNodeBrowserOpen} onOpenChange={setIsNodeBrowserOpen}>
                                        <DialogTrigger asChild>
                                            <Button 
                                                disabled={!workflowJson} 
                                                className="gap-2 bg-gradient-to-r from-primary to-primary/80"
                                            >
                                                <Search className="h-4 w-4" />
                                                浏览节点
                                            </Button>
                                        </DialogTrigger>
                                    </Dialog>
                                    <Button variant="outline" onClick={addParameter} className="gap-2">
                                        <Plus className="h-4 w-4" />
                                        手动添加
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ) : (
                    <>
                        {/* 搜索结果统计 */}
                        {(searchTerm || filterBy !== 'all') && (
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                                <div className="flex items-center gap-2">
                                    <Search className="h-4 w-4" />
                                    <span>
                                        显示 {filteredParameters.length} / {parameters.length} 个参数
                                        {searchTerm && ` (搜索: "${searchTerm}")`}
                                        {filterBy !== 'all' && ` (过滤: ${
                                            filterBy === 'hasPath' ? '有路径' :
                                            filterBy === 'hasDefault' ? '有默认值' : '待完善'
                                        })`}
                                    </span>
                                </div>
                                {filteredParameters.length === 0 && (
                                    <Badge variant="secondary">无匹配结果</Badge>
                                )}
                            </div>
                        )}

                        {/* 参数列表 */}
                        {filteredParameters.length === 0 ? (
                            <Card className="border-dashed">
                                <CardContent className="flex flex-col items-center justify-center py-12">
                                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                                        <Search className="h-8 w-8 text-muted-foreground" />
                                    </div>
                                    <div className="mt-4 text-center">
                                        <h3 className="text-lg font-semibold">未找到匹配的参数</h3>
                                        <p className="mt-2 text-sm text-muted-foreground">
                                            尝试调整搜索条件或清除过滤器
                                        </p>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={clearSearch}
                                            className="mt-4 gap-2"
                                        >
                                            <X className="h-4 w-4" />
                                            清除搜索
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ) : (
                            <div className="grid gap-4">
                                <AnimatePresence>
                                    {filteredParameters.map((param) => (
                                        <motion.div
                                            key={param.key}
                                            layout
                                            variants={itemVariants}
                                            initial="hidden"
                                            animate="visible"
                                            exit="exit"
                                            transition={{ duration: 0.2 }}
                                        >
                                            <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-primary/20 hover:border-l-primary/50 bg-gradient-to-r from-background to-accent/5">
                                                <CardContent className="p-6">
                                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                        <div className="space-y-2">
                                                            <Label className="text-sm font-medium flex items-center gap-2">
                                                                <FileText className="h-4 w-4 text-primary" />
                                                                参数名称
                                                            </Label>
                                                            <Input
                                                                value={param.name}
                                                                onChange={(e) => updateParameter(param.key, 'name', e.target.value)}
                                                                placeholder="输入参数名称"
                                                                className="font-medium"
                                                            />
                                                            <p className="text-xs text-muted-foreground">用户界面中显示的名称</p>
                                                        </div>
                                                        
                                                        <div className="space-y-2">
                                                            <Label className="text-sm font-medium flex items-center gap-2">
                                                                <Code2 className="h-4 w-4 text-primary" />
                                                                节点路径
                                                            </Label>
                                                            <div className="relative">
                                                                <Target className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                                                                <Input
                                                                    value={param.path}
                                                                    onChange={(e) => updateParameter(param.key, 'path', e.target.value)}
                                                                    placeholder="例如: 6.inputs.text"
                                                                    className="pl-10 font-mono text-sm"
                                                                />
                                                            </div>
                                                            <p className="text-xs text-muted-foreground">工作流中的目标节点路径</p>
                                                        </div>
                                                        
                                                        <div className="space-y-2">
                                                            <Label className="text-sm font-medium flex items-center gap-2">
                                                                <Hash className="h-4 w-4 text-primary" />
                                                                默认值
                                                            </Label>
                                                            <Input
                                                                value={param.defaultValue}
                                                                onChange={(e) => updateParameter(param.key, 'defaultValue', e.target.value)}
                                                                placeholder="设置默认值"
                                                            />
                                                            <p className="text-xs text-muted-foreground">参数的初始默认值</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="flex items-center justify-between mt-6 pt-4 border-t">
                                                        <div className="flex items-center gap-2 flex-wrap">
                                                            {param.name && (
                                                                <Badge variant="default" className="text-xs gap-1">
                                                                    <FileText className="h-3 w-3" />
                                                                    {param.name}
                                                                </Badge>
                                                            )}
                                                            {param.path && (
                                                                <Badge variant="secondary" className="text-xs font-mono gap-1">
                                                                    <Target className="h-3 w-3" />
                                                                    {param.path}
                                                                </Badge>
                                                            )}
                                                            {param.defaultValue && (
                                                                <Badge variant="outline" className="text-xs gap-1">
                                                                    <Hash className="h-3 w-3" />
                                                                    {param.defaultValue.toString().substring(0, 20)}
                                                                    {param.defaultValue.toString().length > 20 && '...'}
                                                                </Badge>
                                                            )}
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => deleteParameter(param.key)}
                                                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                                        >
                                                            <Trash2 className="h-4 w-4 mr-1" />
                                                            删除参数
                                                        </Button>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </motion.div>
                                    ))}
                                </AnimatePresence>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
} 