'use client';

import { Button } from '@kit/ui/button';
import { useFormContext } from 'react-hook-form';
import { ArrowLeft, Save, Settings, SlidersHorizontal, Share2, Layers } from 'lucide-react';

export type EditorPane = 'general' | 'parameters' | 'outputs' | 'composite' | 'presets';

const panes: { id: EditorPane; label: string; Icon: React.ElementType }[] = [
    { id: 'general', label: '通用设置', Icon: Settings },
    { id: 'parameters', label: '输入参数', Icon: SlidersHorizontal },
    { id: 'outputs', label: '输出节点', Icon: Share2 },
    { id: 'composite', label: '组合覆写', Icon: Layers },
];

interface ConfigEditorSidebarProps {
    activePane: EditorPane;
    setActivePane: (pane: EditorPane) => void;
    onCancel: () => void;
    isPending?: boolean;
}

export function ConfigEditorSidebar({ activePane, setActivePane, onCancel, isPending }: ConfigEditorSidebarProps) {
    const { formState } = useFormContext();

    return (
        <div className="flex flex-col h-full">
            <div className="flex items-center mb-6">
                <Button variant="ghost" size="icon" onClick={onCancel} className="mr-2">
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <div>
                    <h1 className="text-xl font-bold">编辑配置</h1>
                </div>
            </div>

            <div className="flex-1 space-y-2">
                {panes.map(pane => (
                    <Button
                        key={pane.id}
                        type="button"
                        variant={activePane === pane.id ? 'secondary' : 'ghost'}
                        onClick={() => setActivePane(pane.id)}
                        className="w-full justify-start gap-2"
                        disabled={!formState.isValid && pane.id !== 'general'}
                    >
                        <pane.Icon className="h-4 w-4" />
                        {pane.label}
                    </Button>
                ))}
            </div>

            <div className="mt-auto">
                <Button type="submit" className="w-full gap-2" disabled={isPending || !formState.isValid}>
                    <Save className="h-4 w-4" />
                    {isPending ? '保存中...' : '保存配置'}
                </Button>
            </div>
        </div>
    );
}
