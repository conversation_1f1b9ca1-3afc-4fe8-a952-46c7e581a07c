'use client';

import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';
import { Textarea } from '@kit/ui/textarea';
import type { Database } from '@kit/supabase/database';
import { ComfyUIWorkflow } from '@kit/comfyui/types';
import { Button } from '@kit/ui/button';
import { Wand2 } from 'lucide-react';
import { Trans } from '@kit/ui/trans';

interface ConfigGeneralPaneProps {
    workflows: ComfyUIWorkflow[];
}

export function ConfigGeneralPane({ workflows }: ConfigGeneralPaneProps) {
    const { control } = useFormContext();

    return (
        <Card>
            <CardHeader>
                <CardTitle>通用设置</CardTitle>
                <CardDescription>配置此工作流的基础信息和行为。</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <FormField
                    name="workflow_id"
                    control={control}
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>选择基础工作流</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                    <SelectTrigger>
                                        <SelectValue placeholder="选择一个工作流..." />
                                    </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                    {workflows.map(wf => (
                                        <SelectItem key={wf.id} value={wf.id}>{wf.display_name} ({wf.name})</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        name="display_name"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>显示名称</FormLabel>
                                <Input {...field} placeholder="例如：高清头像生成器" />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="config_name"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>配置名称 (英文标识)</FormLabel>
                                <Input {...field} placeholder="例如：hd-avatar-generator" />
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
                <FormField
                    name="description"
                    control={control}
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>描述</FormLabel>
                            <Textarea {...field} value={field.value ?? ''} placeholder="介绍此配置的用途、特点等。" />
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        name="status"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>状态</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="active">激活</SelectItem>
                                        <SelectItem value="inactive">未激活</SelectItem>
                                        <SelectItem value="deprecated">已弃用</SelectItem>
                                    </SelectContent>
                                </Select>
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="display_order"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>显示顺序</FormLabel>
                                <Input type="number" {...field} onChange={e => field.onChange(parseInt(e.target.value, 10) || 0)} />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                        name="estimated_time_seconds"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>预计执行时间 (秒)</FormLabel>
                                <Input type="number" {...field} value={field.value ?? ''} onChange={e => field.onChange(e.target.value ? parseInt(e.target.value, 10) : null)} />
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="gpu_memory_mb"
                        control={control}
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>预计显存占用 (MB)</FormLabel>
                                <Input type="number" {...field} value={field.value ?? ''} onChange={e => field.onChange(e.target.value ? parseInt(e.target.value, 10) : null)} />
                            </FormItem>
                        )}
                    />
                </div>

                <div className="flex items-center space-x-8 pt-4">
                    <FormField
                        name="is_public"
                        control={control}
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormLabel>
                                    <Trans i18nKey="workflows:public" />
                                </FormLabel>
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="is_featured"
                        control={control}
                        render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                                <FormLabel>
                                    <Trans i18nKey="workflows:featured" />
                                </FormLabel>
                            </FormItem>
                        )}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
