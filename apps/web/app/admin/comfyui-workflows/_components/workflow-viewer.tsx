'use client';

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@kit/ui/accordion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { ArrowLeft, Edit, Plus, Settings, CodeXml } from 'lucide-react';
import type { ComfyUIWorkflow, ComfyUIWorkflowConfigWithWorkflow } from '@kit/comfyui/types';
import { getWorkflowTypeLabel, type WorkflowType } from '../_lib/workflow-type-utils';

import { DescriptionItem, DescriptionList } from './shared/description-list';

interface WorkflowViewerProps {
    workflow: ComfyUIWorkflow;
    configs: ComfyUIWorkflowConfigWithWorkflow[];
    onEdit: () => void;
    onCreateConfig: () => void;
    onEditConfig: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
    onClose: () => void;
    onSelectConfig: (config: ComfyUIWorkflowConfigWithWorkflow) => void;
}

export function WorkflowViewer({
    workflow,
    configs,
    onEdit,
    onCreateConfig,
    onEditConfig,
    onClose,
    onSelectConfig
}: WorkflowViewerProps) {
    const getTypeBadge = (type: string) => {
        return <Badge variant="outline">{getWorkflowTypeLabel(type as WorkflowType)}</Badge>;
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
            active: 'default',
            inactive: 'secondary',
            deprecated: 'destructive',
        };

        const labels: Record<string, string> = {
            active: '活跃',
            inactive: '未激活',
            deprecated: '已弃用'
        };

        return <Badge variant={variants[status] || 'secondary'}>{labels[status] || status}</Badge>;
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="icon" onClick={onClose}>
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <div>
                        <h2 className="text-2xl font-bold">{workflow.display_name}</h2>
                        <p className="text-muted-foreground">{workflow.description || '此工作流暂无描述。'}</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button onClick={onEdit} variant="outline" className="gap-2">
                        <Edit className="h-4 w-4" />
                        编辑工作流
                    </Button>
                    <Button onClick={onCreateConfig} className="gap-2">
                        <Plus className="h-4 w-4" />
                        添加配置
                    </Button>
                </div>
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>工作流详情</CardTitle>
                    <CardDescription>关于此工作流的所有信息概览。</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                    <DescriptionList title="基本信息">
                        <DescriptionItem label="显示名称">{workflow.display_name}</DescriptionItem>
                        <DescriptionItem label="内部名称">{workflow.name}</DescriptionItem>
                        <DescriptionItem label="版本">{workflow.version}</DescriptionItem>
                        <DescriptionItem label="类型">{getTypeBadge(workflow.workflow_type)}</DescriptionItem>
                        <DescriptionItem label="可见性">
                            <Badge variant={workflow.is_public ? 'default' : 'secondary'}>
                                {workflow.is_public ? '公开' : '私有'}
                            </Badge>
                        </DescriptionItem>
                        <DescriptionItem label="标签">
                            <div className="flex flex-wrap gap-1">
                                {workflow.tags?.length ? workflow.tags.map((tag) => (
                                    <Badge key={tag} variant="outline">{tag}</Badge>
                                )) : <span className="text-muted-foreground text-sm">无</span>}
                            </div>
                        </DescriptionItem>
                    </DescriptionList>

                    <DescriptionList title="元数据">
                        <DescriptionItem label="作者ID">{workflow.author_id || '系统'}</DescriptionItem>
                        <DescriptionItem label="创建时间">{new Date(workflow.created_at).toLocaleString('zh-CN')}</DescriptionItem>
                        <DescriptionItem label="最后更新">{new Date(workflow.updated_at).toLocaleString('zh-CN')}</DescriptionItem>
                    </DescriptionList>

                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">相关配置 ({configs.length})</h3>
                        {configs.length > 0 ? (
                            <div className="border rounded-lg">
                                <ul className="divide-y">
                                    {configs.map((config) => (
                                        <li key={config.id} className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => onSelectConfig(config)}>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex items-center gap-3">
                                                    <h4 className="font-semibold text-primary truncate">{config.display_name}</h4>
                                                    <div className="flex items-center gap-2">
                                                        {getStatusBadge(config.status)}
                                                        {config.is_featured && <Badge variant="secondary">推荐</Badge>}
                                                    </div>
                                                </div>
                                                <p className="text-sm text-muted-foreground truncate">
                                                    {config.description || '暂无描述'}
                                                </p>
                                            </div>
                                            <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); onEditConfig(config); }} className="gap-2 ml-4">
                                                <Settings className="h-4 w-4" />
                                                编辑
                                            </Button>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ) : (
                            <div className="text-center py-10 border rounded-lg border-dashed">
                                <h3 className="text-lg font-medium">无相关配置</h3>
                                <p className="text-muted-foreground text-sm mt-1 mb-4">此工作流尚未创建任何配置。</p>
                                <Button onClick={onCreateConfig} size="sm" className="gap-2">
                                    <Plus className="h-4 w-4" />
                                    创建第一个配置
                                </Button>
                            </div>
                        )}
                    </div>

                    <Accordion type="single" collapsible>
                        <AccordionItem value="workflow-json">
                            <AccordionTrigger>
                                <div className="flex items-center gap-2">
                                    <CodeXml className="h-4 w-4" />
                                    <span>查看原始工作流 JSON</span>
                                </div>
                            </AccordionTrigger>
                            <AccordionContent>
                                <div className="bg-muted p-4 rounded-md overflow-auto max-h-96 mt-2">
                                    <pre className="text-xs">{JSON.stringify(workflow.workflow_json, null, 2)}</pre>
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </CardContent>
            </Card>
        </div>
    );
} 