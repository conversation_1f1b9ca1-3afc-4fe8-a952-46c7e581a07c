import { AdminGuard } from '@kit/admin/components/admin-guard';
import { PageBody, PageHeader } from '@kit/ui/page';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createWorkflowService } from '@kit/comfyui/workflow-service';

import { WorkflowManagementContainer } from './_components/workflow-management-container';

export const metadata = {
    title: 'ComfyUI 工作流管理',
};

async function loadData() {
    const client = getSupabaseServerClient();
    const workflowService = createWorkflowService(client);

    try {
        // 并行加载所有数据
        const [workflowsResult, configsResult] = await Promise.all([
            workflowService.getWorkflows({
                isPublic: undefined, // 获取所有工作流（包括非公开的）
            }),
            workflowService.getAvailableConfigs({
                isPublic: undefined, // 获取所有配置（包括非公开的）
            })
        ]);

        return {
            workflows: workflowsResult.workflows || [],
            configs: configsResult.configs || [],
            usageStats: [] // 暂时为空数组，稍后可以加载
        };
    } catch (error) {
        console.error('加载数据失败:', error);
        return {
            workflows: [],
            configs: [],
            usageStats: []
        };
    }
}

async function ComfyUIWorkflowsPage() {
    const data = await loadData();

    return (
        <>
            <PageHeader description={<AppBreadcrumbs />} />
            <PageBody>
                <WorkflowManagementContainer
                    initialWorkflows={data.workflows}
                    initialConfigs={data.configs}
                    initialUsageStats={data.usageStats}
                />
            </PageBody>
        </>
    );
}

export default AdminGuard(ComfyUIWorkflowsPage); 