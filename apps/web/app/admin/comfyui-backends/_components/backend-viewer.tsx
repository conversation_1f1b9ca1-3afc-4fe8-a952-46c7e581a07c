'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { ArrowLeft, Edit, Trash2, TestTube2 } from 'lucide-react';
import type { ComfyUIBackend } from '@kit/comfyui/types';

interface BackendViewerProps {
  backend: ComfyUIBackend;
  onEdit: () => void;
  onDelete: () => void;
  onTestConnection: () => void;
  onClose: () => void;
  isPending?: boolean;
}

export function BackendViewer({ backend, onEdit, onDelete, onTestConnection, onClose, isPending }: BackendViewerProps) {
  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
      active: 'default',
      inactive: 'secondary', 
      maintenance: 'secondary',
      error: 'destructive',
    };

    const labels: Record<string, string> = {
      active: '活跃',
      inactive: '未激活',
      maintenance: '维护中',
      error: '错误',
    };

    return <Badge variant={variants[status] || 'secondary'}>{labels[status] || status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={onClose}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{backend.display_name}</h1>
            <p className="text-muted-foreground">{backend.name}</p>
          </div>
          {getStatusBadge(backend.status)}
        </div>
        <div className="flex gap-2">
          <Button onClick={onTestConnection} variant="outline" disabled={isPending}>
            <TestTube2 className="h-4 w-4 mr-2" />
            测试连接
          </Button>
          <Button onClick={onEdit} variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            编辑
          </Button>
          <Button onClick={onDelete} variant="destructive" disabled={isPending}>
            <Trash2 className="h-4 w-4 mr-2" />
            删除
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>连接信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>端点:</strong> {backend.endpoint_url}
            </div>
            <div>
              <strong>API密钥:</strong> {backend.api_key ? '已设置' : '未设置'}
            </div>
            <div>
              <strong>超时时间:</strong> {backend.timeout_seconds}秒
            </div>
            <div>
              <strong>上次检查:</strong> {backend.last_health_check ? new Date(backend.last_health_check).toLocaleString('zh-CN') : '从未'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>性能配置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>优先级:</strong> {backend.priority}
            </div>
            <div>
              <strong>最大并发:</strong> {backend.max_concurrent_jobs}
            </div>
            <div>
              <strong>GPU内存:</strong> {backend.gpu_memory_gb ? `${backend.gpu_memory_gb}GB` : 'N/A'}
            </div>
            <div>
              <strong>CPU核心:</strong> {backend.cpu_cores || 'N/A'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>统计信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>完成任务:</strong> {backend.total_jobs_completed}
            </div>
            <div>
              <strong>总执行时间:</strong> {Math.round(backend.total_execution_time_seconds / 60)}分钟
            </div>
            <div>
              <strong>平均执行时间:</strong> {backend.average_execution_time_seconds ? `${Math.round(backend.average_execution_time_seconds)}秒` : 'N/A'}
            </div>
            <div>
              <strong>连续失败:</strong> {backend.consecutive_failures}次
            </div>
          </CardContent>
        </Card>

        {backend.description && (
          <Card>
            <CardHeader>
              <CardTitle>描述</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{backend.description}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}