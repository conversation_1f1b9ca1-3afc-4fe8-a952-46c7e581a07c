'use client';

import { use<PERSON><PERSON>, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { <PERSON><PERSON> } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { ArrowLeft, Save } from 'lucide-react';
import { z } from 'zod';
import type { ComfyUIBackend } from '@kit/comfyui/types';

const formSchema = z.object({
  name: z.string().min(1, '名称不能为空'),
  display_name: z.string().min(1, '显示名称不能为空'),
  description: z.string().optional(),
  endpoint_url: z.string().url('请输入有效的URL'),
  api_key: z.string().optional(),
  timeout_seconds: z.number().int().min(1).max(3600),
  priority: z.number().int().min(0),
  max_concurrent_jobs: z.number().int().min(1).max(100),
  gpu_memory_gb: z.number().int().min(1).optional(),
  cpu_cores: z.number().int().min(1).optional(),
  health_check_interval_seconds: z.number().int().min(10).max(3600),
  status: z.enum(['active', 'inactive', 'maintenance', 'error']).optional(),
});

type FormSchemaType = z.infer<typeof formSchema>;

interface BackendEditorProps {
  backend: ComfyUIBackend | null;
  onSave: (backend: ComfyUIBackend) => void;
  onCancel: () => void;
  isPending?: boolean;
}

export function BackendEditor({ backend, onSave, onCancel, isPending }: BackendEditorProps) {
  const isEditing = !!backend?.id;

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: backend?.name || '',
      display_name: backend?.display_name || '',
      description: backend?.description || '',
      endpoint_url: backend?.endpoint_url || '',
      api_key: backend?.api_key || '',
      timeout_seconds: backend?.timeout_seconds || 300,
      priority: backend?.priority || 0,
      max_concurrent_jobs: backend?.max_concurrent_jobs || 1,
      gpu_memory_gb: backend?.gpu_memory_gb || undefined,
      cpu_cores: backend?.cpu_cores || undefined,
      health_check_interval_seconds: backend?.health_check_interval_seconds || 60,
      status: backend?.status || 'inactive',
    },
  });

  const onSubmit = (data: FormSchemaType) => {
    const backendData: ComfyUIBackend = {
      id: backend?.id || '',
      name: data.name,
      display_name: data.display_name,
      description: data.description || null,
      endpoint_url: data.endpoint_url,
      api_key: data.api_key || null,
      timeout_seconds: data.timeout_seconds,
      priority: data.priority,
      max_concurrent_jobs: data.max_concurrent_jobs,
      gpu_memory_gb: data.gpu_memory_gb || null,
      cpu_cores: data.cpu_cores || null,
      supported_models: backend?.supported_models || null,
      last_health_check: backend?.last_health_check || null,
      health_check_interval_seconds: data.health_check_interval_seconds,
      consecutive_failures: backend?.consecutive_failures || 0,
      total_jobs_completed: backend?.total_jobs_completed || 0,
      total_execution_time_seconds: backend?.total_execution_time_seconds || 0,
      average_execution_time_seconds: backend?.average_execution_time_seconds || null,
      config_options: backend?.config_options || {},
      status: data.status || 'inactive',
      created_at: backend?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    onSave(backendData);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" onClick={onCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {isEditing ? '编辑后端配置' : '添加后端配置'}
          </h1>
          <p className="text-muted-foreground">
            配置 ComfyUI 后端服务器的连接和性能参数
          </p>
        </div>
      </div>

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>后端服务器的基本配置信息</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>名称 (英文标识)</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：comfyui-gpu-01" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="display_name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>显示名称</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：主GPU服务器" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                name="description"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="描述这个后端服务器的用途、配置等信息..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>连接配置</CardTitle>
              <CardDescription>后端服务器的连接参数</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <FormField
                name="endpoint_url"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API 端点</FormLabel>
                    <FormControl>
                      <Input placeholder="http://localhost:8188" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="api_key"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API 密钥 (可选)</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="留空如果不需要认证" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="timeout_seconds"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>超时时间 (秒)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="3600"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 300)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>性能配置</CardTitle>
              <CardDescription>后端服务器的性能和调度参数</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  name="priority"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>优先级</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0 = 最高优先级"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="max_concurrent_jobs"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>最大并发任务</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 1)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="health_check_interval_seconds"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>健康检查间隔 (秒)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="10"
                          max="3600"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 60)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="gpu_memory_gb"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GPU 内存 (GB, 可选)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="例如：24"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value, 10) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="cpu_cores"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU 核心数 (可选)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="例如：16"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value, 10) : undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isEditing && (
                <FormField
                  name="status"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>状态</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">活跃</SelectItem>
                          <SelectItem value="inactive">未激活</SelectItem>
                          <SelectItem value="maintenance">维护中</SelectItem>
                          <SelectItem value="error">错误</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button type="submit" disabled={isPending || !form.formState.isValid} className="gap-2">
              <Save className="h-4 w-4" />
              {isPending ? '保存中...' : (isEditing ? '更新配置' : '创建配置')}
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}