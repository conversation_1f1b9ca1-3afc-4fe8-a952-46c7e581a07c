'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Activity } from 'lucide-react';

interface AssignmentsListProps {
  assignments: any[];
}

export function AssignmentsList({ assignments }: AssignmentsListProps) {
  if (assignments.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Activity className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">暂无任务分配记录</h3>
          <p className="text-muted-foreground text-center">
            当有 ComfyUI 任务被分配到后端服务器时，记录将显示在这里。
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>任务分配记录</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {assignments.map((assignment) => (
            <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">{assignment.backend?.display_name || '未知后端'}</p>
                <p className="text-sm text-muted-foreground">
                  分配时间: {new Date(assignment.assigned_at).toLocaleString('zh-CN')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{assignment.status}</Badge>
                {assignment.execution_time_seconds && (
                  <span className="text-sm text-muted-foreground">
                    {assignment.execution_time_seconds}s
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}