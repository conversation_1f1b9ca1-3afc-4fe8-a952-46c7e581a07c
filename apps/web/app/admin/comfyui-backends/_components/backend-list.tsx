'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Edit, Eye, Trash2, TestTube2, Server, Clock, Zap } from 'lucide-react';
import type { ComfyUIBackend } from '@kit/comfyui/types';

interface BackendListProps {
  backends: ComfyUIBackend[];
  onViewBackend: (backend: ComfyUIBackend) => void;
  onEditBackend: (backend: ComfyUIBackend) => void;
  onDeleteBackend: (backend: ComfyUIBackend) => void;
  onTestConnection: (backend: ComfyUIBackend) => void;
  isPending?: boolean;
}

export function BackendList({
  backends,
  onViewBackend,
  onEditBackend,
  onDeleteBackend,
  onTestConnection,
  isPending,
}: BackendListProps) {
  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive'> = {
      active: 'default',
      inactive: 'secondary',
      maintenance: 'secondary',
      error: 'destructive',
    };

    const labels: Record<string, string> = {
      active: '活跃',
      inactive: '未激活',
      maintenance: '维护中',
      error: '错误',
    };

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatLastHealthCheck = (timestamp: string | null) => {
    if (!timestamp) return '从未检查';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
    return `${Math.floor(diffMins / 1440)}天前`;
  };

  if (backends.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Server className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">暂无后端服务器</h3>
          <p className="text-muted-foreground text-center mb-4">
            开始添加 ComfyUI 后端服务器来处理图像生成任务。
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {backends.map((backend) => (
        <Card key={backend.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg mb-1 truncate">
                  {backend.display_name}
                </CardTitle>
                <p className="text-sm text-muted-foreground truncate">
                  {backend.name}
                </p>
              </div>
              <div className="flex flex-col gap-2 ml-2">
                {getStatusBadge(backend.status)}
                <Badge variant="outline" className="text-xs">
                  优先级 {backend.priority}
                </Badge>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4 text-muted-foreground" />
                <span className="truncate">{backend.endpoint_url}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-muted-foreground" />
                <span>最大并发: {backend.max_concurrent_jobs}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>上次检查: {formatLastHealthCheck(backend.last_health_check)}</span>
              </div>
            </div>

            {backend.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {backend.description}
              </p>
            )}

            <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
              <div>完成任务: {backend.total_jobs_completed}</div>
              <div>
                平均耗时: {
                  backend.average_execution_time_seconds 
                    ? `${Math.round(backend.average_execution_time_seconds)}s`
                    : 'N/A'
                }
              </div>
              <div>失败次数: {backend.consecutive_failures}</div>
              <div>
                超时设置: {backend.timeout_seconds}s
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewBackend(backend)}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-1" />
                查看
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEditBackend(backend)}
                disabled={isPending}
              >
                <Edit className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTestConnection(backend)}
                disabled={isPending}
              >
                <TestTube2 className="h-4 w-4" />
              </Button>
              
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDeleteBackend(backend)}
                disabled={isPending}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}