'use client';

import { useState, useTransition } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@kit/ui/tabs';
import { Plus, Server, Activity } from 'lucide-react';
import { toast } from '@kit/ui/sonner';
import type { ComfyUIBackend, ComfyUIBackendAssignment } from '@kit/comfyui/types';

import { BackendList } from './backend-list';
import { BackendEditor } from './backend-editor';
import { BackendViewer } from './backend-viewer';
import { AssignmentsList } from './assignments-list';
import {
  createBackendAction,
  updateBackendAction,
  deleteBackendAction,
  testBackendConnectionAction,
} from '../_lib/server/server-actions';

interface BackendManagementProps {
  initialBackends?: ComfyUIBackend[];
  initialAssignments?: any[];
}

type ViewState =
  | { view: 'list' }
  | { view: 'view-backend'; backendId: string }
  | { view: 'edit-backend'; backendId?: string };

export function ComfyUIBackendManagement({
  initialBackends = [],
  initialAssignments = [],
}: BackendManagementProps) {
  const [backends, setBackends] = useState<ComfyUIBackend[]>(initialBackends);
  const [assignments] = useState<any[]>(initialAssignments);
  const [viewState, setViewState] = useState<ViewState>({ view: 'list' });
  const [activeTab, setActiveTab] = useState<'backends' | 'assignments'>('backends');
  const [isPending, startTransition] = useTransition();

  const currentBackend = backends.find(b => 
    (viewState.view === 'view-backend' || viewState.view === 'edit-backend') && 
    b.id === viewState.backendId
  );

  const handleSaveBackend = (data: ComfyUIBackend) => {
    startTransition(async () => {
      const action = data.id ? updateBackendAction : createBackendAction;
      const result = await action(data);
      
      if (result.success && result.backend) {
        setBackends(current => 
          data.id 
            ? current.map(b => b.id === data.id ? result.backend! : b)
            : [...current, result.backend!]
        );
        toast.success(result.message);
        setViewState({ view: 'view-backend', backendId: result.backend.id });
      } else {
        toast.error(result.message || '保存后端配置失败');
      }
    });
  };

  const handleDeleteBackend = (backend: ComfyUIBackend) => {
    if (!confirm(`确定要删除后端 "${backend.display_name}" 吗？`)) {
      return;
    }

    startTransition(async () => {
      const result = await deleteBackendAction({ id: backend.id });
      
      if (result.success) {
        setBackends(current => current.filter(b => b.id !== backend.id));
        toast.success('后端删除成功');
        setViewState({ view: 'list' });
      } else {
        toast.error(result.message || '删除后端失败');
      }
    });
  };

  const handleTestConnection = (backend: ComfyUIBackend) => {
    startTransition(async () => {
      const result = await testBackendConnectionAction({ id: backend.id });
      
      if (result.success) {
        toast.success('连接测试成功');
        // Refresh backend data to get updated health check info
        setBackends(current => 
          current.map(b => b.id === backend.id ? { ...b, ...result.data } : b)
        );
      } else {
        toast.error(result.message || '连接测试失败');
      }
    });
  };

  const renderContent = () => {
    switch (viewState.view) {
      case 'view-backend':
        if (!currentBackend) return null;
        return (
          <BackendViewer
            backend={currentBackend}
            onEdit={() => setViewState({ view: 'edit-backend', backendId: currentBackend.id })}
            onDelete={() => handleDeleteBackend(currentBackend)}
            onTestConnection={() => handleTestConnection(currentBackend)}
            onClose={() => setViewState({ view: 'list' })}
            isPending={isPending}
          />
        );
        
      case 'edit-backend':
        return (
          <BackendEditor
            backend={currentBackend || null}
            onSave={handleSaveBackend}
            onCancel={() => setViewState(currentBackend 
              ? { view: 'view-backend', backendId: currentBackend.id } 
              : { view: 'list' }
            )}
            isPending={isPending}
          />
        );
        
      default: // 'list'
        return (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <div className="flex items-center justify-between mb-6">
              <TabsList>
                <TabsTrigger value="backends" className="gap-2">
                  <Server className="h-4 w-4" />
                  后端服务器
                </TabsTrigger>
                <TabsTrigger value="assignments" className="gap-2">
                  <Activity className="h-4 w-4" />
                  任务分配
                </TabsTrigger>
              </TabsList>
              
              {activeTab === 'backends' && (
                <Button onClick={() => setViewState({ view: 'edit-backend' })} className="gap-2">
                  <Plus className="h-4 w-4" />
                  添加后端
                </Button>
              )}
            </div>

            <TabsContent value="backends">
              <BackendList
                backends={backends}
                onViewBackend={(backend) => setViewState({ view: 'view-backend', backendId: backend.id })}
                onEditBackend={(backend) => setViewState({ view: 'edit-backend', backendId: backend.id })}
                onDeleteBackend={handleDeleteBackend}
                onTestConnection={handleTestConnection}
                isPending={isPending}
              />
            </TabsContent>

            <TabsContent value="assignments">
              <AssignmentsList assignments={assignments} />
            </TabsContent>
          </Tabs>
        );
    }
  };

  return (
    <div className="space-y-6">
      <AnimatePresence mode="wait">
        <motion.div
          key={viewState.view}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
        >
          {renderContent()}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}