import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function loadBackendsPage() {
  const client = getSupabaseServerClient();

  try {
    // Load backends
    const { data: backends, error: backendsError } = await client
      .from('comfyui_backends')
      .select('*')
      .order('priority', { ascending: true });

    if (backendsError) {
      throw backendsError;
    }

    // Load recent assignments
    const { data: assignments, error: assignmentsError } = await client
      .from('comfyui_backend_assignments')
      .select(`
        *,
        backend:backend_id(name, display_name),
        execution:execution_id(account_id, config_id, status)
      `)
      .order('assigned_at', { ascending: false })
      .limit(100);

    if (assignmentsError) {
      throw assignmentsError;
    }

    return {
      backends: backends || [],
      assignments: assignments || [],
    };
  } catch (error) {
    console.error('Error loading backends page:', error);
    return {
      backends: [],
      assignments: [],
    };
  }
}