'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { z } from 'zod';
import { CreateComfyUIBackendSchema, UpdateComfyUIBackendSchema } from '@kit/comfyui/types';

/**
 * 创建后端配置
 */
export const createBackendAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();

    const { data: backend, error } = await client
      .from('comfyui_backends')
      .insert({
        name: data.name,
        display_name: data.display_name,
        description: data.description,
        endpoint_url: data.endpoint_url,
        api_key: data.api_key,
        timeout_seconds: data.timeout_seconds || 300,
        priority: data.priority || 0,
        max_concurrent_jobs: data.max_concurrent_jobs || 1,
        gpu_memory_gb: data.gpu_memory_gb,
        cpu_cores: data.cpu_cores,
        supported_models: data.supported_models || [],
        health_check_interval_seconds: data.health_check_interval_seconds || 60,
        config_options: data.config_options || {},
        status: 'inactive',
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { 
      success: true, 
      backend,
      message: '后端配置创建成功' 
    };
  },
  {
    auth: true,
    schema: CreateComfyUIBackendSchema,
  },
);

/**
 * 更新后端配置
 */
export const updateBackendAction = enhanceAction(
  async function (data, user) {
    const { id, ...updateData } = data;
    
    const client = getSupabaseServerClient();

    const { data: backend, error } = await client
      .from('comfyui_backends')
      .update({
        name: updateData.name,
        display_name: updateData.display_name,
        description: updateData.description,
        endpoint_url: updateData.endpoint_url,
        api_key: updateData.api_key,
        timeout_seconds: updateData.timeout_seconds,
        priority: updateData.priority,
        max_concurrent_jobs: updateData.max_concurrent_jobs,
        gpu_memory_gb: updateData.gpu_memory_gb,
        cpu_cores: updateData.cpu_cores,
        supported_models: updateData.supported_models,
        health_check_interval_seconds: updateData.health_check_interval_seconds,
        config_options: updateData.config_options,
        status: updateData.status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { 
      success: true, 
      backend,
      message: '后端配置更新成功' 
    };
  },
  {
    auth: true,
    schema: UpdateComfyUIBackendSchema,
  },
);

/**
 * 删除后端配置
 */
export const deleteBackendAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();

    const { error } = await client
      .from('comfyui_backends')
      .delete()
      .eq('id', data.id);

    if (error) {
      throw error;
    }

    return { 
      success: true, 
      message: '后端配置删除成功' 
    };
  },
  {
    auth: true,
    schema: z.object({
      id: z.string().uuid(),
    }),
  },
);

/**
 * 测试后端连接
 */
export const testBackendConnectionAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();

    // 获取后端配置
    const { data: backend, error: fetchError } = await client
      .from('comfyui_backends')
      .select('*')
      .eq('id', data.id)
      .single();

    if (fetchError || !backend) {
      throw new Error('后端配置不存在');
    }

    try {
      // 测试连接到 ComfyUI API
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), backend.timeout_seconds * 1000);

      const response = await fetch(`${backend.endpoint_url}/system_stats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(backend.api_key ? { 'Authorization': `Bearer ${backend.api_key}` } : {}),
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const isHealthy = response.ok;
      const now = new Date().toISOString();

      // 更新健康检查状态
      const { data: updatedBackend, error: updateError } = await client
        .from('comfyui_backends')
        .update({
          last_health_check: now,
          consecutive_failures: isHealthy ? 0 : backend.consecutive_failures + 1,
          status: isHealthy ? 'active' : (backend.consecutive_failures >= 2 ? 'error' : backend.status),
          updated_at: now,
        })
        .eq('id', data.id)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      if (!isHealthy) {
        throw new Error(`连接失败: HTTP ${response.status}`);
      }

      return { 
        success: true, 
        data: updatedBackend,
        message: '连接测试成功' 
      };
    } catch (error: any) {
      // 更新失败状态
      await client
        .from('comfyui_backends')
        .update({
          last_health_check: new Date().toISOString(),
          consecutive_failures: backend.consecutive_failures + 1,
          status: backend.consecutive_failures >= 2 ? 'error' : backend.status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', data.id);

      throw new Error(`连接测试失败: ${error.message}`);
    }
  },
  {
    auth: true,
    schema: z.object({
      id: z.string().uuid(),
    }),
  },
);