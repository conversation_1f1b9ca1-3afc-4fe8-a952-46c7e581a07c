import { AdminGuard } from '@kit/admin/components/admin-guard';
import { PageBody, PageHeader } from '@kit/ui/page';

import { ComfyUIBackendManagement } from './_components/backend-management';
import { loadBackendsPage } from './_lib/server/loaders/load-backends-page';

async function ComfyUIBackendsPage() {
  const data = await loadBackendsPage();

  return (
    <>
      <PageHeader 
        title="ComfyUI 后端管理"
        description="管理和配置 ComfyUI 后端服务器"
      />

      <PageBody>
        <ComfyUIBackendManagement 
          initialBackends={data.backends}
          initialAssignments={data.assignments}
        />
      </PageBody>
    </>
  );
}

export default AdminGuard(ComfyUIBackendsPage);