'use client';

import { useEffect, useState } from 'react';
import { Music, Volume2 } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Separator } from '@kit/ui/separator';
import { Slider } from '@kit/ui/slider';
import { Switch } from '@kit/ui/switch';

import { ProjectSettingsFormData } from '../../../../_lib/schema/project-settings.schema';
import { getAvailableConfigsAction } from '../../../../_lib/server/server-actions';

interface AudioSettingsProps {
  form: UseFormReturn<ProjectSettingsFormData>;
}

export function AudioSettings({ form }: AudioSettingsProps) {
  const { t } = useTranslation('novel2video');
  const [configs, setConfigs] = useState<any[]>([]);
  const [loadingConfigs, setLoadingConfigs] = useState(false);

  // 加载可用的音频工作流配置
  useEffect(() => {
    const loadConfigs = async () => {
      setLoadingConfigs(true);
      try {
        const result = await getAvailableConfigsAction({ workflow_type: 'tts' });
        if (result.success && result.configs) {
          setConfigs(result.configs);
        }
      } catch (error) {
        console.error('Failed to load audio workflow configs:', error);
      } finally {
        setLoadingConfigs(false);
      }
    };

    loadConfigs();
  }, []);

  return (
    <div className="space-y-6">
      {/* 基础配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Volume2 className="h-5 w-5" />
            {t('projectSettings.audio.basicConfig')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="audio.workflowConfigId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  工作流配置
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || ''}
                  disabled={loadingConfigs}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={loadingConfigs ? "加载配置中..." : "选择音频生成工作流"}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {configs.map((config) => (
                      <SelectItem key={config.id} value={config.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{config.display_name}</span>
                          {config.description && (
                            <span className="text-xs text-muted-foreground">{config.description}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  选择用于生成音频的TTS工作流配置。不同配置会影响音频生成的音质和风格。
                </FormDescription>
              </FormItem>
            )}
          />

        </CardContent>
      </Card>

      {/* 高级选项 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Music className="h-5 w-5" />
            {t('projectSettings.audio.advancedOptions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="audio.enableEmotionalTone"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('projectSettings.audio.emotionalTone.label')}
                  </FormLabel>
                  <FormDescription>
                    {t('projectSettings.audio.emotionalTone.description')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="audio.backgroundMusic"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('projectSettings.audio.backgroundMusic.label')}
                  </FormLabel>
                  <FormDescription>
                    {t('projectSettings.audio.backgroundMusic.description')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {form.watch('audio.backgroundMusic') && (
            <FormField
              control={form.control}
              name="audio.musicVolume"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('projectSettings.audio.musicVolume.label', {
                      volume: field.value,
                    })}
                  </FormLabel>
                  <FormControl>
                    <div className="px-3">
                      <Slider
                        min={0}
                        max={100}
                        step={5}
                        value={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                        className="w-full"
                      />
                      <div className="text-muted-foreground mt-1 flex justify-between text-xs">
                        <span>0%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
