'use client';

import { useState, useTransition } from 'react';

import { useTranslation } from 'react-i18next';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { But<PERSON> } from '@kit/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { toast } from '@kit/ui/sonner';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { updateSegmentAction } from '../../../_lib/server/server-actions';

const EditSegmentSchema = z.object({
  text_content: z
    .string()
    .min(1, 'novel2video:project.segments.edit.errors.textRequired')
    .max(1000, 'novel2video:project.segments.edit.errors.textTooLong'),
});

type EditSegmentFormData = z.infer<typeof EditSegmentSchema>;

interface EditSegmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  segment: {
    id: string;
    segment_index: number;
    text_content: string;
  } | null;
  onSuccess: (segmentId: string, newTextContent: string) => void;
}

export function EditSegmentDialog({
  open,
  onOpenChange,
  segment,
  onSuccess,
}: EditSegmentDialogProps) {
  const { t } = useTranslation('novel2video');
  const [isPending, startTransition] = useTransition();

  const form = useForm<EditSegmentFormData>({
    resolver: zodResolver(EditSegmentSchema),
    defaultValues: {
      text_content: segment?.text_content || '',
    },
  });

  // 当segment变化时重置表单
  useState(() => {
    if (segment) {
      form.reset({
        text_content: segment.text_content,
      });
    }
  });

  const onSubmit = (data: EditSegmentFormData) => {
    if (!segment) return;

    startTransition(async () => {
      try {
        const result = await updateSegmentAction({
          segmentId: segment.id,
          textContent: data.text_content,
        });

        if (result.success) {
          toast.success(
            <Trans i18nKey="novel2video:project.segments.edit.success" />,
          );
          onSuccess(segment.id, data.text_content);
          onOpenChange(false);
        } else {
          toast.error(
            result.message || (
              <Trans i18nKey="novel2video:project.segments.edit.error" />
            ),
          );
        }
      } catch (error) {
        console.error('编辑片段失败:', error);
        toast.error(<Trans i18nKey="common:genericError" />);
      }
    });
  };

  const handleClose = () => {
    if (!isPending) {
      onOpenChange(false);
      form.reset();
    }
  };

  if (!segment) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            <Trans
              i18nKey="novel2video:project.segments.edit.title"
              values={{ index: segment.segment_index }}
            />
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="text_content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="novel2video:project.segments.edit.contentLabel" />
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      rows={6}
                      placeholder={t('project.segments.edit.textPlaceholder')}
                      className="resize-none"
                    />
                  </FormControl>
                  <div className="text-muted-foreground flex justify-between text-xs">
                    <FormMessage />
                    <span>{field.value?.length || 0}/1000</span>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                <Trans i18nKey="common:cancel" />
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <Trans i18nKey="novel2video:project.segments.edit.saving" />
                ) : (
                  <Trans i18nKey="novel2video:project.segments.edit.save" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
