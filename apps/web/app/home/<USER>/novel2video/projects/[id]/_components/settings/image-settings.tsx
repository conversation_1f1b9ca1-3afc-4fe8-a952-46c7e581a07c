'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, Palette, Settings } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';

import { ProjectSettingsFormData } from '../../../../_lib/schema/project-settings.schema';
import { getAvailableConfigsAction } from '../../../../_lib/server/server-actions';

interface ImageSettingsProps {
  form: UseFormReturn<ProjectSettingsFormData>;
}

export function ImageSettings({ form }: ImageSettingsProps) {
  const { t } = useTranslation('novel2video');
  const [configs, setConfigs] = useState<any[]>([]);
  const [loadingConfigs, setLoadingConfigs] = useState(false);

  // 加载可用的工作流配置
  useEffect(() => {
    const loadConfigs = async () => {
      setLoadingConfigs(true);
      try {
        const result = await getAvailableConfigsAction({ workflow_type: 'txt2img' });
        if (result.success && result.configs) {
          setConfigs(result.configs);
        }
      } catch (error) {
        console.error('Failed to load workflow configs:', error);
      } finally {
        setLoadingConfigs(false);
      }
    };

    loadConfigs();
  }, []);

  return (
    <div className="space-y-6">
      {/* 风格配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Image className="h-5 w-5" />
            {t('projectSettings.image.styleConfig')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="image.style"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('projectSettings.image.style.label')}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.image.style.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="realistic">
                      {t('projectSettings.image.style.realistic')}
                    </SelectItem>
                    <SelectItem value="artistic">
                      {t('projectSettings.image.style.artistic')}
                    </SelectItem>
                    <SelectItem value="anime">
                      {t('projectSettings.image.style.anime')}
                    </SelectItem>
                    <SelectItem value="cartoon">
                      {t('projectSettings.image.style.cartoon')}
                    </SelectItem>
                    <SelectItem value="cinematic">
                      {t('projectSettings.image.style.cinematic')}
                    </SelectItem>
                    <SelectItem value="watercolor">
                      {t('projectSettings.image.style.watercolor')}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image.aspectRatio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.image.aspectRatio.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.image.aspectRatio.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="16:9">16:9</SelectItem>
                    <SelectItem value="9:16">9:16</SelectItem>
                    <SelectItem value="1:1">1:1</SelectItem>
                    <SelectItem value="4:3">4:3</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image.quality"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.image.quality.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.image.quality.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="standard">
                      {t('projectSettings.image.quality.standard')}
                    </SelectItem>
                    <SelectItem value="high">
                      {t('projectSettings.image.quality.high')}
                    </SelectItem>
                    <SelectItem value="ultra">
                      {t('projectSettings.image.quality.ultra')}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image.workflowConfigId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  工作流配置
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || ''}
                  disabled={loadingConfigs}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={loadingConfigs ? "加载配置中..." : "选择图像生成工作流"}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {configs.map((config) => (
                      <SelectItem key={config.id} value={config.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{config.display_name}</span>
                          {config.description && (
                            <span className="text-xs text-muted-foreground">{config.description}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  选择用于生成图像的AI工作流配置。不同配置会影响图像生成的风格和质量。
                </FormDescription>
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* 效果配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Palette className="h-5 w-5" />
            {t('projectSettings.image.effectConfig')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="image.colorPalette"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.image.colorPalette.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.image.colorPalette.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="vibrant">
                      {t('projectSettings.image.colorPalette.vibrant')}
                    </SelectItem>
                    <SelectItem value="pastel">
                      {t('projectSettings.image.colorPalette.pastel')}
                    </SelectItem>
                    <SelectItem value="monochrome">
                      {t('projectSettings.image.colorPalette.monochrome')}
                    </SelectItem>
                    <SelectItem value="warm">
                      {t('projectSettings.image.colorPalette.warm')}
                    </SelectItem>
                    <SelectItem value="cool">
                      {t('projectSettings.image.colorPalette.cool')}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image.lightingStyle"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.image.lightingStyle.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.image.lightingStyle.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="natural">
                      {t('projectSettings.image.lightingStyle.natural')}
                    </SelectItem>
                    <SelectItem value="dramatic">
                      {t('projectSettings.image.lightingStyle.dramatic')}
                    </SelectItem>
                    <SelectItem value="soft">
                      {t('projectSettings.image.lightingStyle.soft')}
                    </SelectItem>
                    <SelectItem value="goldenHour">
                      {t('projectSettings.image.lightingStyle.goldenHour')}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="image.enableFaceEnhancement"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('projectSettings.image.faceEnhancement.label')}
                  </FormLabel>
                  <FormDescription>
                    {t('projectSettings.image.faceEnhancement.description')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </CardContent>
      </Card>
    </div>
  );
}
