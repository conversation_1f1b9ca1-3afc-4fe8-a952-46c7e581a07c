/* 项目进度动画样式 */
@keyframes card-glow {
  0% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0);
  }
}

.animate-card-glow {
  animation: card-glow 0.6s ease-out;
}

/* 进度条动画 */
@keyframes progress-pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.progress-animated .progress-fill {
  animation: progress-pulse 2s ease-in-out infinite;
}

/* 段落卡片悬停效果 */
.segment-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.segment-card:hover {
  transform: translateY(-2px);
}

/* 网格项目悬停效果增强 */
.grid-item-hover {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.grid-item-hover:hover {
  transform: translateY(-4px) scale(1.02);
}

/* 表格行悬停效果 */
.table-row-hover {
  transition: background-color 0.15s ease-in-out;
}

.table-row-hover:hover {
  background-color: hsl(var(--muted) / 0.3);
}

/* 徽章动画 */
.badge-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 步骤指示器 */
.step-indicator {
  position: relative;
}

.step-indicator::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -20px;
  width: 40px;
  height: 2px;
  background: hsl(var(--border));
  transform: translateY(-50%);
}

.step-indicator:last-child::after {
  display: none;
}

/* 提示词展开动画 */
.prompt-expand {
  transition: max-height 0.3s ease-in-out;
}

/* 图像缩略图悬停效果 */
.image-thumbnail {
  transition: transform 0.2s ease-in-out;
}

.image-thumbnail:hover {
  transform: scale(1.1);
}

/* 状态指示器 */
.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-dot.pending {
  background-color: hsl(var(--warning));
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-dot.completed {
  background-color: hsl(var(--success));
}

.status-dot.processing {
  background-color: hsl(var(--primary));
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-dot.failed {
  background-color: hsl(var(--destructive));
}

/* 加载指示器 */
.loading-skeleton {
  background: linear-gradient(90deg, 
    hsl(var(--muted)) 25%, 
    hsl(var(--muted) / 0.5) 50%, 
    hsl(var(--muted)) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 分页动画 */
.pagination-fade-in {
  animation: fade-in-up 0.3s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停发光效果 */
.btn-glow:hover {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
}

/* 数字动画效果 */
.animate-number-bounce {
  animation: number-bounce 0.4s ease-out;
}

@keyframes number-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.animate-subtle-glow {
  animation: subtle-glow 1.5s ease-in-out;
}

@keyframes subtle-glow {
  0%, 100% {
    text-shadow: none;
  }
  50% {
    text-shadow: 0 0 8px hsl(var(--primary) / 0.6);
  }
}

/* 进度条脉冲效果 */
.animate-progress-pulse {
  animation: progress-pulse-enhanced 0.6s ease-out;
}

@keyframes progress-pulse-enhanced {
  0% {
    transform: scale(1);
    text-shadow: none;
  }
  50% {
    transform: scale(1.05);
    text-shadow: 0 0 10px hsl(var(--primary) / 0.8);
  }
  100% {
    transform: scale(1);
    text-shadow: none;
  }
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.4);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.6);
}

/* 搜索框焦点动画 */
.search-focus {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-focus:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* 工具栏渐现动画 */
.toolbar-slide-in {
  animation: slide-in-down 0.4s ease-out;
}

@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}