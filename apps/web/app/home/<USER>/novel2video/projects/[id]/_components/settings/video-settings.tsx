'use client';

import { MonitorPlay, Video } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';

import { ProjectSettingsFormData } from '../../../../_lib/schema/project-settings.schema';

interface VideoSettingsProps {
  form: UseFormReturn<ProjectSettingsFormData>;
}

export function VideoSettings({ form }: VideoSettingsProps) {
  const { t } = useTranslation('novel2video');

  return (
    <div className="space-y-6">
      {/* 输出配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <Video className="h-5 w-5" />
            {t('projectSettings.video.outputConfig')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="video.resolution"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.video.resolution.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.video.resolution.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="720p">720p HD</SelectItem>
                    <SelectItem value="1080p">1080p Full HD</SelectItem>
                    <SelectItem value="1440p">1440p 2K</SelectItem>
                    <SelectItem value="2160p">2160p 4K</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="video.frameRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('projectSettings.video.frameRate.label')}
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.video.frameRate.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="24">24 fps</SelectItem>
                    <SelectItem value="30">30 fps</SelectItem>
                    <SelectItem value="60">60 fps</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="video.format"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('projectSettings.video.format.label')}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t(
                          'projectSettings.video.format.placeholder',
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="mp4">MP4</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* 效果选项 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <MonitorPlay className="h-5 w-5" />
            {t('projectSettings.video.effectOptions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="video.enableTransitions"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('projectSettings.video.transitions.label')}
                  </FormLabel>
                  <FormDescription>
                    {t('projectSettings.video.transitions.description')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {form.watch('video.enableTransitions') && (
            <FormField
              control={form.control}
              name="video.transitionStyle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('projectSettings.video.transitionStyle.label')}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t(
                            'projectSettings.video.transitionStyle.placeholder',
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="fade">
                        {t('projectSettings.video.transitionStyle.fade')}
                      </SelectItem>
                      <SelectItem value="slide">
                        {t('projectSettings.video.transitionStyle.slide')}
                      </SelectItem>
                      <SelectItem value="zoom">
                        {t('projectSettings.video.transitionStyle.zoom')}
                      </SelectItem>
                      <SelectItem value="dissolve">
                        {t('projectSettings.video.transitionStyle.dissolve')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="video.enableSubtitles"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('projectSettings.video.subtitles.label')}
                  </FormLabel>
                  <FormDescription>
                    {t('projectSettings.video.subtitles.description')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {form.watch('video.enableSubtitles') && (
            <FormField
              control={form.control}
              name="video.subtitleStyle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('projectSettings.video.subtitleStyle.label')}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t(
                            'projectSettings.video.subtitleStyle.placeholder',
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="modern">
                        {t('projectSettings.video.subtitleStyle.modern')}
                      </SelectItem>
                      <SelectItem value="classic">
                        {t('projectSettings.video.subtitleStyle.classic')}
                      </SelectItem>
                      <SelectItem value="minimal">
                        {t('projectSettings.video.subtitleStyle.minimal')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
