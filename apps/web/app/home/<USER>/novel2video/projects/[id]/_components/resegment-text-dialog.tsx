'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { toast } from '@kit/ui/sonner';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { resegmentProjectAction } from '../../../_lib/server/server-actions';

const ResegmentSchema = z.object({
  text: z
    .string()
    .min(1, '文本内容不能为空')
    .max(50000, '文本内容不能超过50000字符'),
});

type ResegmentFormData = z.infer<typeof ResegmentSchema>;

interface ResegmentTextDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: {
    id: string;
    title: string;
    original_text: string;
  };
  onSuccess: () => void;
}

export function ResegmentTextDialog({
  open,
  onOpenChange,
  project,
  onSuccess,
}: ResegmentTextDialogProps) {
  const [isPending, startTransition] = useTransition();

  const form = useForm<ResegmentFormData>({
    resolver: zodResolver(ResegmentSchema),
    defaultValues: {
      text: project.original_text || '',
    },
  });

  // 当project变化时重置表单
  useState(() => {
    if (project) {
      form.reset({
        text: project.original_text || '',
      });
    }
  });

  const onSubmit = (data: ResegmentFormData) => {
    startTransition(async () => {
      try {
        const result = await resegmentProjectAction({
          projectId: project.id,
          newText: data.text,
        });

        if (result.success) {
          toast.success(
            <Trans i18nKey="novel2video:project.segments.resegment.success" />,
          );
          onSuccess();
          onOpenChange(false);
        } else {
          toast.error(
            result.message || (
              <Trans i18nKey="novel2video:project.segments.resegment.error" />
            ),
          );
        }
      } catch (error) {
        console.error('重新分割文本失败:', error);
        toast.error(<Trans i18nKey="common:genericError" />);
      }
    });
  };

  const handleClose = () => {
    if (!isPending) {
      onOpenChange(false);
      form.reset();
    }
  };

  const textLength = form.watch('text')?.length || 0;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[80vh] sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            <Trans i18nKey="novel2video:project.segments.resegment.title" />
          </DialogTitle>
          <DialogDescription>
            <Trans i18nKey="novel2video:project.segments.resegment.description" />
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950/20">
            <div className="flex items-start gap-2">
              <AlertTriangle className="mt-0.5 h-4 w-4 flex-shrink-0 text-amber-600 dark:text-amber-400" />
              <div className="text-sm">
                <p className="mb-1 font-medium text-amber-800 dark:text-amber-200">
                  <Trans i18nKey="novel2video:project.segments.resegment.warning.title" />
                </p>
                <p className="text-amber-700 dark:text-amber-300">
                  <Trans i18nKey="novel2video:project.segments.resegment.warning.description" />
                </p>
              </div>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Trans i18nKey="novel2video:project.segments.resegment.textLabel" />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        rows={12}
                        placeholder="请输入要分割的文本内容..."
                        className="resize-none font-mono text-sm"
                      />
                    </FormControl>
                    <div className="text-muted-foreground flex justify-between text-xs">
                      <FormMessage />
                      <span
                        className={textLength > 40000 ? 'text-amber-600' : ''}
                      >
                        <Trans
                          i18nKey="novel2video:project.segments.resegment.textCount"
                          values={{ count: textLength, max: 50000 }}
                          defaults="{count}/{max} characters"
                        />
                      </span>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isPending}
                >
                  <Trans i18nKey="common:cancel" />
                </Button>
                <Button type="submit" disabled={isPending || textLength === 0}>
                  {isPending ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      <Trans i18nKey="novel2video:project.segments.resegment.processing" />
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      <Trans i18nKey="novel2video:project.segments.resegment.confirm" />
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
