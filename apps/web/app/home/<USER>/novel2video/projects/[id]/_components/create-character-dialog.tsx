'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { toast } from '@kit/ui/sonner';
import { Trans } from '@kit/ui/trans';

import { createCharacterAction } from '../../../_lib/server/server-actions';

const CreateCharacterSchema = z.object({
    name: z.string().min(1, 'novel2video:project.characters.create.errors.nameRequired').max(255, 'novel2video:project.characters.create.errors.nameTooLong'),
    aliases: z.string().optional(),
    imagePrompt: z.string().optional(),
});

type CreateCharacterData = z.infer<typeof CreateCharacterSchema>;

interface CreateCharacterDialogProps {
    projectId: string;
    onSuccess: () => void;
}

export function CreateCharacterDialog({ projectId, onSuccess }: CreateCharacterDialogProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const form = useForm<CreateCharacterData>({
        resolver: zodResolver(CreateCharacterSchema),
        defaultValues: {
            name: '',
            aliases: '',
            imagePrompt: '',
        },
    });

    const createMutation = useMutation({
        mutationFn: async (data: CreateCharacterData) => {
            const result = await createCharacterAction({
                projectId,
                name: data.name,
                aliases: data.aliases ? data.aliases.split(',').map(s => s.trim()).filter(s => s.length > 0) : [],
                imagePrompt: data.imagePrompt,
            });
            if (!result.success) {
                throw new Error(result.error || 'novel2video:project.characters.create.errors.createFailed');
            }
            return result;
        },
        onSuccess: (result) => {
            toast.success(result.message || '人物创建成功');
            onSuccess();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const onSubmit = async (data: CreateCharacterData) => {
        setIsSubmitting(true);
        try {
            await createMutation.mutateAsync(data);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <DialogHeader>
                <DialogTitle>
                    <Trans i18nKey="novel2video:createCharacterTitle" defaults="添加人物" />
                </DialogTitle>
            </DialogHeader>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    <Trans i18nKey="novel2video:characterName" defaults="人物名称" />
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="输入人物名称"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="aliases"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    <Trans i18nKey="novel2video:characterAliases" defaults="别名" />
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="多个别名用逗号分隔，如：张真人, 老道"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="imagePrompt"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>
                                    <Trans i18nKey="novel2video:imagePrompt" defaults="形象描述" />
                                </FormLabel>
                                <FormControl>
                                    <Textarea
                                        placeholder="描述人物的外貌特征、服装等（可选）"
                                        rows={3}
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            type="submit"
                            disabled={isSubmitting || createMutation.isPending}
                        >
                            {isSubmitting ? '创建中...' : '创建人物'}
                        </Button>
                    </div>
                </form>
            </Form>
        </>
    );
} 