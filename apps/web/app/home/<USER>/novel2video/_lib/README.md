# Novel2Video Library Structure

## 概述

该目录包含Novel2Video功能的所有服务端逻辑，已经进行了模块化重构以提高可维护性。

## 文件结构

```
_lib/
├── server/
│   ├── actions/                    # Server Actions (按功能分组)
│   │   ├── index.ts               # Actions导出入口
│   │   ├── project-actions.ts     # 项目管理相关actions
│   │   ├── segment-actions.ts     # 文本段落管理actions
│   │   ├── task-actions.ts        # 任务管理actions
│   │   ├── image-actions.ts       # 图像生成相关actions
│   │   ├── character-actions.ts   # 人物管理actions
│   │   └── comfyui-actions.ts     # ComfyUI配置actions
│   ├── server-actions.ts          # 主入口文件 (重导出所有actions)
│   ├── novel2video-api.ts         # 数据库API
│   └── text-processor.ts          # 文本处理工具
├── types/
│   └── server-action-schemas.ts   # 类型定义和验证模式
├── utils/
│   └── image-dimensions.ts        # 图像尺寸工具函数
├── hooks/
│   └── use-task-realtime.ts       # 实时任务状态hook
└── README.md                      # 本文件
```

## 模块说明

### Actions 模块

所有server actions按功能进行了分组：

#### 1. Project Actions (`project-actions.ts`)
- 项目的CRUD操作
- 项目处理流程控制

#### 2. Segment Actions (`segment-actions.ts`)
- 文本段落的管理
- 文本重新切分

#### 3. Task Actions (`task-actions.ts`)
- 异步任务状态查询
- 任务控制（取消、重试）

#### 4. Image Actions (`image-actions.ts`)
- 图像提示词生成
- 图像生成任务创建
- 批量操作

#### 5. Character Actions (`character-actions.ts`)
- 人物提取和管理
- 段落人物分析

#### 6. ComfyUI Actions (`comfyui-actions.ts`)
- 工作流配置查询
- 连接状态检查

### 类型定义 (`types/server-action-schemas.ts`)

包含所有server actions的：
- Zod验证模式
- TypeScript类型定义
- 输入参数类型

### 工具函数 (`utils/image-dimensions.ts`)

图像相关的工具函数：
- 宽高比转换
- 尺寸计算
- 显示格式化

## 使用方式

### 导入Actions

```typescript
// 导入所有actions
import * as actions from '~/app/home/<USER>/novel2video/_lib/server/server-actions';

// 或者导入特定actions
import { 
  createProjectAction,
  generatePromptsAction 
} from '~/app/home/<USER>/novel2video/_lib/server/server-actions';
```

### 类型使用

```typescript
import type { 
  CreateProjectInput,
  GeneratePromptsInput 
} from '~/app/home/<USER>/novel2video/_lib/types/server-action-schemas';
```

### 工具函数使用

```typescript
import { 
  getImageDimensions,
  formatDimensions 
} from '~/app/home/<USER>/novel2video/_lib/utils/image-dimensions';
```

## 优势

1. **模块化**: 按功能分组，易于维护和查找
2. **类型安全**: 统一的类型定义和验证
3. **可重用**: 工具函数可在多处使用
4. **向后兼容**: 主入口文件保持原有的导出接口
5. **文档清晰**: 每个模块职责明确

## 迁移指南

现有代码无需修改，因为主入口文件（`server-actions.ts`）仍然导出所有原有的actions。

如果需要优化导入，可以考虑直接从具体模块导入：

```typescript
// 之前
import { createProjectAction } from './server-actions';

// 现在（可选优化）
import { createProjectAction } from './actions/project-actions';
```