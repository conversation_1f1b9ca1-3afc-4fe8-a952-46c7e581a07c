import { z } from 'zod';

// 项目设置的 Zod schema - 音频参数由工作流配置决定
export const ProjectSettingsSchema = z.object({
  audio: z.object({
    workflowConfigId: z.string().optional(),
    // 高级选项 - 这些不影响ComfyUI工作流参数
    enableEmotionalTone: z.boolean().default(false),
    backgroundMusic: z.boolean().default(false),
    musicVolume: z.number().min(0).max(100).default(30),
  }),
  image: z.object({
    style: z.enum([
      'realistic',
      'artistic',
      'anime',
      'cartoon',
      'cinematic',
      'watercolor',
    ]),
    aspectRatio: z.enum(['16:9', '9:16', '1:1', '4:3']),
    quality: z.enum(['standard', 'high', 'ultra']),
    colorPalette: z.enum(['vibrant', 'pastel', 'monochrome', 'warm', 'cool']),
    enableFaceEnhancement: z.boolean(),
    lightingStyle: z.enum(['natural', 'dramatic', 'soft', 'goldenHour']),
    workflowConfigId: z.string().optional(),
  }),
  video: z.object({
    resolution: z.enum(['480p', '720p', '1080p', '1440p', '2160p']),
    frameRate: z.enum(['24', '30', '60']),
    format: z.enum(['mp4', 'mov', 'avi', 'webm']),
    enableTransitions: z.boolean(),
    transitionStyle: z.enum(['fade', 'slide', 'zoom', 'dissolve']),
    enableSubtitles: z.boolean(),
    subtitleStyle: z.enum(['modern', 'classic', 'minimal']),
  }),
});

export type ProjectSettingsFormData = z.infer<typeof ProjectSettingsSchema>;
export type ProjectSettings = ProjectSettingsFormData; // 类型别名

// 默认设置
export const getDefaultSettings = (): ProjectSettingsFormData => ({
  audio: {
    workflowConfigId: undefined,
    enableEmotionalTone: false,
    backgroundMusic: false,
    musicVolume: 30,
  },
  image: {
    style: 'realistic',
    aspectRatio: '16:9',
    quality: 'high',
    colorPalette: 'vibrant',
    enableFaceEnhancement: true,
    lightingStyle: 'natural',
    workflowConfigId: undefined,
  },
  video: {
    resolution: '1080p',
    frameRate: '30',
    format: 'mp4',
    enableTransitions: true,
    transitionStyle: 'fade',
    enableSubtitles: false,
    subtitleStyle: 'modern',
  },
});

export const defaultProjectSettings = getDefaultSettings(); // 导出默认设置常量

// 更新项目设置的 schema
export const UpdateProjectSettingsSchema = z.object({
  projectId: z.string().uuid(),
  settings: ProjectSettingsSchema,
});

export type UpdateProjectSettingsData = z.infer<
  typeof UpdateProjectSettingsSchema
>;
