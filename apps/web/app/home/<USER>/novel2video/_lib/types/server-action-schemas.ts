import { z } from 'zod';
import { Database } from '~/lib/database.types';

// 创建项目的验证模式
export const CreateProjectSchema = z.object({
  title: z.string().min(1, '项目标题不能为空').max(255, '项目标题过长'),
  description: z.string().optional(),
  text: z
    .string()
    .min(10, '文本内容至少需要10个字符')
    .max(50000, '文本内容过长'),
});

// 更新项目的验证模式
export const UpdateProjectSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  status: z.enum(['draft', 'processing', 'completed', 'failed']).optional(),
  progress: z.number().min(0).max(100).optional(),
  settings: z.record(z.unknown()).optional(),
});

// 删除项目的验证模式
export const DeleteProjectSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

// 更新文本片段的验证模式
export const UpdateSegmentSchema = z.object({
  segmentId: z.string().uuid('无效的片段ID'),
  textContent: z.string().min(1, '文本内容不能为空').max(1000, '文本内容过长'),
});

// 删除文本片段的验证模式
export const DeleteSegmentSchema = z.object({
  segmentId: z.string().uuid('无效的片段ID'),
});

// 重新切分文本的验证模式
export const ResegmentProjectSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  newText: z
    .string()
    .min(10, '文本内容至少需要10个字符')
    .max(50000, '文本内容过长'),
});

// 提示词生成相关模式
export const GeneratePromptsSchema = z.object({
  projectId: z.string().uuid(),
  style: z.string().optional(),
  context: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

// 高级图像生成参数
export const GenerateImageSchema = z.object({
  project_id: z.string().uuid('项目ID必须是有效的UUID'),
  segment_id: z.string().uuid('段落ID必须是有效的UUID'),
  negative_prompt: z.string().optional(),
  width: z.number().int().min(512).max(2048).default(1024),
  height: z.number().int().min(512).max(2048).default(1024),
  steps: z.number().int().min(1).max(100).default(20),
  cfg: z.number().min(1).max(20).default(7),
  seed: z.number().int().optional(),
});

// 快速生成参数  
export const QuickGenerateSchema = z.object({
  project_id: z.string().uuid('项目ID必须是有效的UUID'),
  segment_id: z.string().uuid('段落ID必须是有效的UUID'),
  style: z.enum(['realistic', 'anime', 'artistic', 'photographic'], {
    errorMap: () => ({ message: '样式必须是 realistic, anime, artistic, photographic 中的一个' })
  }),
  aspect_ratio: z.enum(['1:1', '16:9', '9:16', '4:3', '3:4'], {
    errorMap: () => ({ message: '宽高比必须是 1:1, 16:9, 9:16, 4:3, 3:4 中的一个' })
  }),
  quality: z.enum(['draft', 'standard', 'high'], {
    errorMap: () => ({ message: '质量必须是 draft, standard, high 中的一个' })
  }),
});

// 获取配置列表参数
export const GetConfigsSchema = z.object({
  workflow_type: z.custom<Database['public']['Enums']['comfyui_workflow_type']>().optional(),
});

// 一键生成图片的验证模式
export const GenerateImagesSchema = z.object({
  projectId: z.string().uuid(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

// 人物提取相关的验证模式
export const ExtractCharactersSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

export const CreateCharacterSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  name: z.string().min(1, '人物名称不能为空').max(255, '人物名称过长'),
  aliases: z.array(z.string().max(255, '别名过长')).optional(),
  imagePrompt: z.string().optional(),
});

export const UpdateCharacterSchema = z.object({
  characterId: z.string().uuid('无效的人物ID'),
  name: z.string().min(1, '人物名称不能为空').max(255, '人物名称过长').optional(),
  aliases: z.array(z.string().max(255, '别名过长')).optional(),
  imagePrompt: z.string().optional(),
});

export const DeleteCharacterSchema = z.object({
  characterId: z.string().uuid('无效的人物ID'),
});

export const ExtractSegmentCharactersSchema = z.object({
  segmentId: z.string().uuid('无效的段落ID'),
});

// 音频生成相关的验证模式
export const GenerateAudioSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

export const GenerateSingleSegmentAudioSchema = z.object({
  segmentId: z.string().uuid('无效的段落ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

export const RegenerateAllAudioSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

// 任务相关模式
export const GetTaskStatusSchema = z.object({
  taskId: z.string().uuid(),
});

export const GetProjectTasksSchema = z.object({
  projectId: z.string().uuid(),
});

export const CancelTaskSchema = z.object({
  taskId: z.string().uuid(),
});

// 通用模式
export const ProjectIdSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

export const ConfigIdSchema = z.object({
  configId: z.string().uuid(),
});

export const EmptySchema = z.object({});

// 批量分析段落人物的验证模式
export const AnalyzeSegmentCharactersSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

// 单个段落生成提示词的验证模式
export const GenerateSingleSegmentPromptSchema = z.object({
  segmentId: z.string().uuid('无效的段落ID'),
  style: z.string().optional(),
  context: z.string().optional(),
});

// 项目处理相关模式
export const StartProcessingSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  style: z.string().optional(),
  context: z.string().optional(),
});

export const PauseProcessingSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

// 视频合成相关的验证模式
export const MergeVideoSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

export const CheckVideoReadinessSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

export const DeleteVideoSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
});

// 类型定义
export type CreateProjectInput = z.infer<typeof CreateProjectSchema>;
export type UpdateProjectInput = z.infer<typeof UpdateProjectSchema>;
export type DeleteProjectInput = z.infer<typeof DeleteProjectSchema>;
export type UpdateSegmentInput = z.infer<typeof UpdateSegmentSchema>;
export type DeleteSegmentInput = z.infer<typeof DeleteSegmentSchema>;
export type ResegmentProjectInput = z.infer<typeof ResegmentProjectSchema>;
export type GeneratePromptsInput = z.infer<typeof GeneratePromptsSchema>;
export type GenerateImageInput = z.infer<typeof GenerateImageSchema>;
export type QuickGenerateInput = z.infer<typeof QuickGenerateSchema>;
export type GetConfigsInput = z.infer<typeof GetConfigsSchema>;
export type GenerateImagesInput = z.infer<typeof GenerateImagesSchema>;
export type ExtractCharactersInput = z.infer<typeof ExtractCharactersSchema>;
export type CreateCharacterInput = z.infer<typeof CreateCharacterSchema>;
export type UpdateCharacterInput = z.infer<typeof UpdateCharacterSchema>;
export type DeleteCharacterInput = z.infer<typeof DeleteCharacterSchema>;
export type ExtractSegmentCharactersInput = z.infer<typeof ExtractSegmentCharactersSchema>;
export type GetTaskStatusInput = z.infer<typeof GetTaskStatusSchema>;
export type GetProjectTasksInput = z.infer<typeof GetProjectTasksSchema>;
export type CancelTaskInput = z.infer<typeof CancelTaskSchema>;
export type ProjectIdInput = z.infer<typeof ProjectIdSchema>;
export type ConfigIdInput = z.infer<typeof ConfigIdSchema>;
export type AnalyzeSegmentCharactersInput = z.infer<typeof AnalyzeSegmentCharactersSchema>;
export type GenerateSingleSegmentPromptInput = z.infer<typeof GenerateSingleSegmentPromptSchema>;
export type StartProcessingInput = z.infer<typeof StartProcessingSchema>;
export type PauseProcessingInput = z.infer<typeof PauseProcessingSchema>;
export type GenerateAudioInput = z.infer<typeof GenerateAudioSchema>;
export type GenerateSingleSegmentAudioInput = z.infer<typeof GenerateSingleSegmentAudioSchema>;
export type RegenerateAllAudioInput = z.infer<typeof RegenerateAllAudioSchema>;
export type MergeVideoInput = z.infer<typeof MergeVideoSchema>;
export type CheckVideoReadinessInput = z.infer<typeof CheckVideoReadinessSchema>;
export type DeleteVideoInput = z.infer<typeof DeleteVideoSchema>;