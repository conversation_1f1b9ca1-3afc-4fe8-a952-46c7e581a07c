'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoApi } from '../novel2video-api';
import { splitTextIntoSegments } from '../text-processor';

import {
  UpdateSegmentSchema,
  DeleteSegmentSchema,
  ResegmentProjectSchema,
  type UpdateSegmentInput,
  type DeleteSegmentInput,
  type ResegmentProjectInput,
} from '../../types/server-action-schemas';

/**
 * 更新文本片段内容
 * Update text segment content
 */
export const updateSegmentAction = enhanceAction(
  async function (data: UpdateSegmentInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    const segment = await api.updateSegment(data.segmentId, {
      text_content: data.textContent,
    });

    return {
      success: true,
      data: segment,
      message: '片段更新成功！',
    };
  },
  {
    auth: true,
    schema: UpdateSegmentSchema,
  },
);

/**
 * 删除文本片段
 * Delete text segment
 */
export const deleteSegmentAction = enhanceAction(
  async function (data: DeleteSegmentInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    await api.deleteSegment(data.segmentId);

    return {
      success: true,
      message: '片段删除成功！',
    };
  },
  {
    auth: true,
    schema: DeleteSegmentSchema,
  },
);

/**
 * 重新切分项目文本
 * Re-segment project text
 */
export const resegmentProjectAction = enhanceAction(
  async function (data: ResegmentProjectInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    // 删除现有的所有片段
    await api.deleteAllSegments(data.projectId);

    // 重新切分文本
    const segments = splitTextIntoSegments(data.newText);

    if (segments.length > 0) {
      // 创建新的文本片段
      await api.createSegments(
        data.projectId,
        segments.map((text, index) => ({ index, text })),
      );
    }

    // 更新项目的原始文本
    await api.updateProject(
      data.projectId,
      {
        original_text: data.newText,
        progress: 0,
        status: 'draft',
      },
      user.id,
    );

    return {
      success: true,
      data: { segmentCount: segments.length },
      message: `文本重新切分成功！共生成 ${segments.length} 个片段`,
    };
  },
  {
    auth: true,
    schema: ResegmentProjectSchema,
  },
);