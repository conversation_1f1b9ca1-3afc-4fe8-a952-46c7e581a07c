'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoTaskApi } from '@kit/novel2video/api';

import {
  GetTaskStatusSchema,
  GetProjectTasksSchema,
  CancelTaskSchema,
  type GetTaskStatusInput,
  type GetProjectTasksInput,
  type CancelTaskInput,
} from '../../types/server-action-schemas';

export const getTaskStatusAction = enhanceAction(
  async function (data: GetTaskStatusInput, user) {
    const supabase = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(supabase);

    const task = await taskApi.getTaskStatus(data.taskId);

    return {
      success: true,
      task,
    };
  },
  {
    auth: true,
    schema: GetTaskStatusSchema,
  },
);

export const getProjectTasksAction = enhanceAction(
  async function (data: GetProjectTasksInput, user) {
    const supabase = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(supabase);

    const tasks = await taskApi.getProjectTasks(data.projectId);

    return {
      success: true,
      tasks,
    };
  },
  {
    auth: true,
    schema: GetProjectTasksSchema,
  },
);

export const cancelTaskAction = enhanceAction(
  async function (data: CancelTaskInput, user) {
    const supabase = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(supabase);

    const task = await taskApi.cancelTask(data.taskId);

    return {
      success: true,
      task,
    };
  },
  {
    auth: true,
    schema: CancelTaskSchema,
  },
);

export const retryTaskAction = enhanceAction(
  async function (data: CancelTaskInput, user) {
    const supabase = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(supabase);

    const task = await taskApi.retryTask(data.taskId);

    return {
      success: true,
      task,
    };
  },
  {
    auth: true,
    schema: CancelTaskSchema, // 复用同样的schema
  },
);