'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import {
  ProjectSettingsFormData,
  UpdateProjectSettingsSchema,
} from '../schema/project-settings.schema';

/**
 * 更新项目设置的 Server Action
 */
export const updateProjectSettingsAction = enhanceAction(
  async function updateProjectSettings(data, user) {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    logger.info('Updating project settings', {
      projectId: data.projectId,
      userId: user.id,
    });

    try {
      // 更新项目设置
      const { data: project, error } = await client
        .from('novel2video_projects')
        .update({
          settings: data.settings,
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        })
        .eq('id', data.projectId)
        .select('id, title, settings')
        .single();

      if (error) {
        logger.error('Failed to update project settings', {
          error: error.message,
          projectId: data.projectId,
          userId: user.id,
        });

        throw error;
      }

      logger.info('Project settings updated successfully', {
        projectId: data.projectId,
        userId: user.id,
      });

      return {
        success: true,
        data: project,
      };
    } catch (error) {
      logger.error('Error updating project settings', {
        error: error instanceof Error ? error.message : 'Unknown error',
        projectId: data.projectId,
        userId: user.id,
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '更新设置失败',
      };
    }
  },
  {
    auth: true,
    schema: UpdateProjectSettingsSchema,
  },
);

/**
 * 获取项目设置的 Server Action
 */
export const getProjectSettingsAction = enhanceAction(
  async function getProjectSettings(data, user) {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    logger.info('Getting project settings', {
      projectId: data.projectId,
      userId: user.id,
    });

    try {
      const { data: project, error } = await client
        .from('novel2video_projects')
        .select('id, title, settings')
        .eq('id', data.projectId)
        .single();

      if (error) {
        logger.error('Failed to get project settings', {
          error: error.message,
          projectId: data.projectId,
          userId: user.id,
        });

        throw error;
      }

      logger.info('Project settings retrieved successfully', {
        projectId: data.projectId,
        userId: user.id,
      });

      return {
        success: true,
        data: {
          id: project.id,
          title: project.title,
          settings: project.settings as ProjectSettingsFormData | null,
        },
      };
    } catch (error) {
      logger.error('Error getting project settings', {
        error: error instanceof Error ? error.message : 'Unknown error',
        projectId: data.projectId,
        userId: user.id,
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : '获取设置失败',
      };
    }
  },
  {
    auth: true,
    schema: z.object({
      projectId: z.string().uuid(),
    }),
  },
);
