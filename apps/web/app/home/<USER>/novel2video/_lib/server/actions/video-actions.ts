'use server';

import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoApi } from '../novel2video-api';
import { createNovel2VideoTaskApi } from '@kit/novel2video/api';

// 视频合成相关的验证模式
const MergeVideoSchema = z.object({
  projectId: z.string().uuid('无效的项目ID'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
});

type MergeVideoInput = z.infer<typeof MergeVideoSchema>;

/**
 * 删除存储中的视频文件
 * Delete video file from storage
 */
async function deleteVideoFromStorage(filePath: string): Promise<void> {
  const client = getSupabaseServerClient();
  
  const { error } = await client.storage
    .from('generated-videos')
    .remove([filePath]);
    
  if (error) {
    console.error('Failed to delete video file from storage:', error);
    // 不抛出错误，因为数据库记录已经删除了
  }
}

/**
 * 取消项目的视频合成任务
 * Cancel video synthesis tasks for a project
 */
async function cancelVideoTasks(projectId: string): Promise<void> {
  const client = getSupabaseServerClient();
  
  const { error } = await client
    .from('novel2video_tasks')
    .update({
      status: 'cancelled',
      updated_at: new Date().toISOString(),
    })
    .eq('project_id', projectId)
    .eq('task_type', 'merge_video')
    .in('status', ['pending', 'running']);

  if (error) {
    throw new Error(`取消现有任务失败: ${error.message}`);
  }
}

/**
 * 删除项目的视频记录和文件
 * Delete project video record and file
 */
async function deleteProjectVideo(projectId: string): Promise<void> {
  const client = getSupabaseServerClient();
  
  // 先获取视频文件路径
  const { data: video } = await client
    .from('novel2video_videos')
    .select('file_path')
    .eq('project_id', projectId)
    .single();
    
  // 删除数据库记录
  const { error: deleteError } = await client
    .from('novel2video_videos')
    .delete()
    .eq('project_id', projectId);

  if (deleteError) {
    throw new Error(`删除视频记录失败: ${deleteError.message}`);
  }
  
  // 删除存储文件
  if (video?.file_path) {
    await deleteVideoFromStorage(video.file_path);
  }
}

/**
 * 合成最终视频
 * Merge final video from all segments
 */
export const mergeVideoAction = enhanceAction(
  async function (data: MergeVideoInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取项目完整信息
    const projectDetails = await api.getProjectWithDetails(data.projectId);
    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    if (projectDetails.segments.length === 0) {
      throw new Error('项目没有文本段落');
    }

    // 检查是否所有段落都有音频和图片文件
    const segmentsWithoutMedia = projectDetails.segments.filter(
      segment => !segment.audioFile || !segment.imageFile
    );

    if (segmentsWithoutMedia.length > 0) {
      throw new Error(`还有 ${segmentsWithoutMedia.length} 个段落缺少音频或图片文件，请先完成所有媒体生成`);
    }

    // 检查是否已经有视频文件
    if (projectDetails.video) {
      throw new Error('项目已经生成了视频文件，如需重新生成请先删除现有视频');
    }

    // 获取项目设置
    let videoSettings = {
      resolution: '1080p',
      format: 'mp4',
      frameRate: 30,
      aspectRatio: '16:9',
      transitionDuration: 0.5,
      audioFadeInOut: true,
    };

    try {
      if (projectDetails.project.settings && typeof projectDetails.project.settings === 'object') {
        const settings = projectDetails.project.settings as any;
        if (settings.video) {
          videoSettings = { ...videoSettings, ...settings.video };
        }
      }
    } catch (error) {
      console.warn('Failed to parse project video settings, using defaults:', error);
    }

    // 创建视频合成任务
    const task = await taskApi.createMergeVideoTask({
      accountId: projectDetails.project.account_id,
      projectId: data.projectId,
      parameters: {
        videoSettings,
        segmentCount: projectDetails.segments.length,
      },
      priority: data.priority || 'normal',
    });

    // 更新项目状态
    await api.updateProject(
      data.projectId,
      {
        status: 'processing',
      },
      user.id,
    );

    return {
      success: true,
      taskId: task.id,
      message: `已启动视频合成任务，将处理 ${projectDetails.segments.length} 个段落`,
      videoSettings,
    };
  },
  {
    auth: true,
    schema: MergeVideoSchema,
  },
);

/**
 * 检查项目是否准备好进行视频合成
 * Check if project is ready for video merging
 */
export const checkVideoReadinessAction = enhanceAction(
  async function (data: { projectId: string }, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    // 获取项目完整信息
    const projectDetails = await api.getProjectWithDetails(data.projectId);
    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    const totalSegments = projectDetails.segments.length;
    const segmentsWithAudio = projectDetails.segments.filter(s => s.audioFile).length;
    const segmentsWithImage = projectDetails.segments.filter(s => s.imageFile).length;
    const segmentsWithBoth = projectDetails.segments.filter(s => s.audioFile && s.imageFile).length;

    const isReady = totalSegments > 0 && segmentsWithBoth === totalSegments;
    const hasVideo = !!projectDetails.video;

    return {
      success: true,
      data: {
        isReady,
        hasVideo,
        totalSegments,
        segmentsWithAudio,
        segmentsWithImage,
        segmentsWithBoth,
        missingMedia: totalSegments - segmentsWithBoth,
      },
    };
  },
  {
    auth: true,
    schema: z.object({
      projectId: z.string().uuid('无效的项目ID'),
    }),
  },
);

/**
 * 删除现有视频文件
 * Delete existing video file
 */
export const deleteVideoAction = enhanceAction(
  async function (data: { projectId: string }, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    // 获取项目信息
    const projectDetails = await api.getProjectWithDetails(data.projectId);
    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    if (!projectDetails.video) {
      throw new Error('项目没有视频文件');
    }

    // 删除视频记录和文件
    await deleteProjectVideo(data.projectId);

    return {
      success: true,
      message: '视频文件已删除',
    };
  },
  {
    auth: true,
    schema: z.object({
      projectId: z.string().uuid('无效的项目ID'),
    }),
  },
);

/**
 * 重新合成视频
 * Regenerate video from all segments
 */
export const regenerateVideoAction = enhanceAction(
  async function (data: MergeVideoInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取项目完整信息
    const projectDetails = await api.getProjectWithDetails(data.projectId);
    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    if (projectDetails.segments.length === 0) {
      throw new Error('项目没有文本段落');
    }

    // 检查是否所有段落都有音频和图片文件
    const segmentsWithoutMedia = projectDetails.segments.filter(
      segment => !segment.audioFile || !segment.imageFile
    );

    if (segmentsWithoutMedia.length > 0) {
      throw new Error(`还有 ${segmentsWithoutMedia.length} 个段落缺少音频或图片文件，请先完成所有媒体生成`);
    }

    // 如果有现有视频，先删除
    if (projectDetails.video) {
      await deleteProjectVideo(data.projectId);
    }

    // 取消所有该项目的正在运行的视频合成任务
    await cancelVideoTasks(data.projectId);

    // 获取项目设置
    let videoSettings = {
      resolution: '1080p',
      format: 'mp4',
      frameRate: 30,
      aspectRatio: '16:9',
      transitionDuration: 0.5,
      audioFadeInOut: true,
    };

    try {
      if (projectDetails.project.settings && typeof projectDetails.project.settings === 'object') {
        const settings = projectDetails.project.settings as any;
        if (settings.video) {
          videoSettings = { ...videoSettings, ...settings.video };
        }
      }
    } catch (error) {
      console.warn('Failed to parse project video settings, using defaults:', error);
    }

    // 创建新的视频合成任务
    const task = await taskApi.createMergeVideoTask({
      accountId: projectDetails.project.account_id,
      projectId: data.projectId,
      parameters: {
        videoSettings,
        segmentCount: projectDetails.segments.length,
        isRegeneration: true, // 标记为重新生成
      },
      priority: data.priority || 'high', // 重新生成使用高优先级
    });

    // 更新项目状态
    await api.updateProject(
      data.projectId,
      {
        status: 'processing',
      },
      user.id,
    );

    return {
      success: true,
      taskId: task.id,
      message: `已启动视频重新合成任务，将处理 ${projectDetails.segments.length} 个段落`,
      videoSettings,
    };
  },
  {
    auth: true,
    schema: MergeVideoSchema,
  },
); 