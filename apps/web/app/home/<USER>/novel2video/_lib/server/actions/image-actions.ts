'use server';

import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoApi } from '../novel2video-api';
import { createNovel2VideoTaskApi } from '@kit/novel2video/api';
import { getImageDimensions } from '../../utils/image-dimensions';

import {
  GeneratePromptsSchema,
  GenerateImageSchema,
  QuickGenerateSchema,
  GenerateImagesSchema,
  GenerateSingleSegmentPromptSchema,
  type GeneratePromptsInput,
  type GenerateImageInput,
  type QuickGenerateInput,
  type GenerateImagesInput,
  type GenerateSingleSegmentPromptInput,
} from '../../types/server-action-schemas';

/**
 * 通用的提示词生成逻辑
 */
async function createPromptGenerationTasks(
  client: any,
  taskApi: any,
  projectId: string,
  style: string,
  context: string,
  priority: string,
  includeCompleted: boolean = false
) {
  // 获取项目信息和权限检查
  const { data: project, error: projectError } = await client
    .from('novel2video_projects')
    .select('account_id')
    .eq('id', projectId)
    .single();

  if (projectError || !project) {
    throw new Error('项目不存在');
  }

  let segmentsToProcess = [];

  if (includeCompleted) {
    // 重新生成：获取所有段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('项目中没有找到段落');
    }

    segmentsToProcess = segments;
  } else {
    // 只生成未完成的：查询没有提示词的段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId)
      .is('image_prompt', null);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('没有待处理的段落（所有段落都已生成提示词）');
    }

    segmentsToProcess = segments;
  }

  const segmentIds = segmentsToProcess.map((s: any) => s.id);

  // 创建任务
  const task = await taskApi.createGeneratePromptsTask({
    accountId: project.account_id,
    projectId: projectId,
    segmentIds: segmentIds,
    style: style,
    context: context,
    priority: priority,
  });

  return {
    task,
    segmentCount: segmentIds.length,
    actionType: includeCompleted ? '重新生成' : '生成'
  };
}

/**
 * 通用的图片生成逻辑
 */
async function createImageGenerationTasks(
  client: any,
  taskApi: any,
  projectId: string,
  priority: string,
  includeCompleted: boolean = false
) {
  // 获取项目信息和设置
  const { data: project, error: projectError } = await client
    .from('novel2video_projects')
    .select('account_id, settings')
    .eq('id', projectId)
    .single();

  if (projectError || !project) {
    throw new Error('项目不存在');
  }

  let segmentsToProcess = [];

  if (includeCompleted) {
    // 重新生成：查询所有有提示词的段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId)
      .not('image_prompt', 'is', null);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('没有可重新生成图片的段落（需要先生成提示词）');
    }

    segmentsToProcess = segments;
  } else {
    // 只生成未完成的：查询有提示词但没有图片文件的段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select(`
        id,
        image_prompt,
        novel2video_image_files(id)
      `)
      .eq('project_id', projectId)
      .not('image_prompt', 'is', null);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('没有待处理的段落（需要先生成提示词）');
    }

    // 筛选出没有图片文件的段落
    segmentsToProcess = segments.filter((segment: any) => 
      !segment.novel2video_image_files || segment.novel2video_image_files.length === 0
    );

    if (segmentsToProcess.length === 0) {
      throw new Error('没有待处理的段落（所有段落都已生成图片）');
    }
  }

  // 获取项目设置中的图像配置
  let imageSettings = {
    aspectRatio: '16:9',
    style: 'realistic',
    quality: 'high',
    workflowConfigId: null as string | null
  };
  
  let videoSettings = {
    resolution: '1080p'
  };

  try {
    if (project.settings && typeof project.settings === 'object') {
      const settings = project.settings as any;
      if (settings.image) {
        imageSettings = { ...imageSettings, ...settings.image };
      }
      if (settings.video) {
        videoSettings = { ...videoSettings, ...settings.video };
      }
    }
  } catch (error) {
    console.warn('Failed to parse project settings, using defaults:', error);
  }

  // 根据项目设置计算图像尺寸
  const { width, height } = getImageDimensions(imageSettings.aspectRatio, videoSettings.resolution);

  // 从项目设置中获取工作流配置ID
  const configId = imageSettings.workflowConfigId;
  
  if (!configId) {
    throw new Error('请先在项目设置中配置图像生成工作流');
  }

  // 验证项目配置的工作流是否仍然可用
  const { data: config, error: configError } = await client
    .from('comfyui_workflow_configs')
    .select('id, config_name, display_name')
    .eq('id', configId)
    .eq('status', 'active')
    .single();

  if (configError || !config) {
    throw new Error('项目配置的工作流已不可用，请在项目设置中重新选择');
  }
  
  const configName = config.display_name || config.config_name;

  // 为每个段落创建图像生成任务
  const tasks = [];
  for (const segment of segmentsToProcess) {
    const task = await taskApi.createGenerateImagesTask({
      accountId: project.account_id,
      projectId: projectId,
      segmentId: segment.id,
      configId: configId,
      parameters: {
        width,
        height,
        style: imageSettings.style,
        quality: imageSettings.quality,
      },
      priority: priority,
    });
    tasks.push(task);
  }

  return {
    tasks,
    segmentCount: segmentsToProcess.length,
    actionType: includeCompleted ? '重新生成' : '生成',
    configName,
    imageSettings: {
      width,
      height,
      aspectRatio: imageSettings.aspectRatio,
      resolution: videoSettings.resolution,
    }
  };
}

/**
 * 为所有待处理段落生成图像提示词（异步任务）
 * Generate image prompts for all pending segments (async task)
 */
export const generateAllSegmentPromptsAction = enhanceAction(
  async function (data: GeneratePromptsInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createPromptGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.style || '写实风格',
      data.context || '小说场景描述',
      data.priority || 'normal',
      false // includeCompleted = false
    );

    return {
      success: true,
      taskId: result.task.id,
      message: `已启动图像提示词${result.actionType}任务，处理 ${result.segmentCount} 个段落`,
    };
  },
  {
    auth: true,
    schema: GeneratePromptsSchema,
  },
);


/**
 * 为单个段落生成图像提示词（异步任务）
 * Generate image prompt for a single segment (async task)
 */
export const generateSingleSegmentPromptAction = enhanceAction(
  async function (data: GenerateSingleSegmentPromptInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取段落信息
    const { data: segment, error: segmentError } = await client
      .from('novel2video_segments')
      .select('id, project_id')
      .eq('id', data.segmentId)
      .single();

    if (segmentError || !segment) {
      throw new Error('段落不存在');
    }

    // 获取项目信息和权限检查
    const { data: project, error: projectError } = await client
      .from('novel2video_projects')
      .select('account_id')
      .eq('id', segment.project_id)
      .single();

    if (projectError || !project) {
      throw new Error('项目不存在');
    }

    // 创建异步任务
    const task = await taskApi.createGeneratePromptsTask({
      accountId: project.account_id,
      projectId: segment.project_id,
      segmentIds: [data.segmentId],
      style: data.style || '写实风格',
      context: data.context || '小说场景描述',
      priority: 'high', // 单个段落使用高优先级
    });

    return {
      success: true,
      taskId: task.id,
      message: `已启动单个段落的图像提示词生成任务`,
    };
  },
  {
    auth: true,
    schema: GenerateSingleSegmentPromptSchema,
  },
);



/**
 * 为单个段落生成图片（基于批量生成的逻辑）
 * Generate image for a single segment (based on batch generation logic)
 */
export const generateSingleSegmentImageAction = enhanceAction(
  async function (data: { segmentId: string }, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取段落信息
    const { data: segment, error: segmentError } = await client
      .from('novel2video_segments')
      .select('id, project_id, image_prompt')
      .eq('id', data.segmentId)
      .single();

    if (segmentError || !segment) {
      throw new Error('段落不存在');
    }

    // 检查段落是否已有提示词
    if (!segment.image_prompt) {
      throw new Error('段落还没有生成提示词，请先生成提示词');
    }

    // 获取项目信息和设置
    const { data: project, error: projectError } = await client
      .from('novel2video_projects')
      .select('account_id, settings')
      .eq('id', segment.project_id)
      .single();

    if (projectError || !project) {
      throw new Error('项目不存在');
    }

    // 获取项目设置中的图像配置
    let imageSettings = {
      aspectRatio: '16:9',
      style: 'realistic',
      quality: 'high',
      workflowConfigId: null as string | null
    };
    
    let videoSettings = {
      resolution: '1080p'
    };

    try {
      if (project.settings && typeof project.settings === 'object') {
        const settings = project.settings as any;
        if (settings.image) {
          imageSettings = { ...imageSettings, ...settings.image };
        }
        if (settings.video) {
          videoSettings = { ...videoSettings, ...settings.video };
        }
      }
    } catch (error) {
      console.warn('Failed to parse project settings, using defaults:', error);
    }

    // 根据项目设置计算图像尺寸
    const { width, height } = getImageDimensions(imageSettings.aspectRatio, videoSettings.resolution);

    // 从项目设置中获取工作流配置ID
    const configId = imageSettings.workflowConfigId;
    
    if (!configId) {
      throw new Error('请先在项目设置中配置图像生成工作流');
    }

    // 验证项目配置的工作流是否仍然可用
    const { error: configError, data: config } = await client
      .from('comfyui_workflow_configs')
      .select('id, config_name, display_name')
      .eq('id', configId)
      .eq('status', 'active')
      .single();

    if (configError || !config) {
      throw new Error('项目配置的工作流已不可用，请在项目设置中重新选择');
    }
    
    const configName = config.display_name || config.config_name;

    // 为段落创建图像生成任务
    const task = await taskApi.createGenerateImagesTask({
      accountId: project.account_id,
      projectId: segment.project_id,
      segmentId: segment.id,
      configId: configId,
      parameters: {
        width,
        height,
        style: imageSettings.style,
        quality: imageSettings.quality,
      },
      priority: 'high', // 单个段落使用高优先级
    });

    return {
      success: true,
      taskId: task.id,
      message: `已启动段落图像生成任务（${width}x${height}），使用配置：${configName}`,
      imageSettings: {
        width,
        height,
        aspectRatio: imageSettings.aspectRatio,
        resolution: videoSettings.resolution,
      },
    };
  },
  {
    auth: true,
    schema: z.object({
      segmentId: z.string().min(1, '段落ID不能为空'),
    }),
  },
);

/**
 * 为所有有提示词的段落生成图片（批量操作）
 * Generate images for all segments with prompts (batch operation)
 */
export const generateAllSegmentImagesAction = enhanceAction(
  async function (data: GenerateImagesInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createImageGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.priority || 'normal',
      false // includeCompleted = false
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动图像${result.actionType}任务，处理 ${result.segmentCount} 个段落（${result.imageSettings.width}x${result.imageSettings.height}），使用配置：${result.configName}`,
      imageSettings: result.imageSettings,
    };
  },
  {
    auth: true,
    schema: GenerateImagesSchema,
  },
);

/**
 * 重新生成所有提示词（包括已完成的段落）
 * Re-generate prompts for all segments (including completed segments)
 */
export const regenerateAllPromptsAction = enhanceAction(
  async function (data: GeneratePromptsInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createPromptGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.style || '写实风格',
      data.context || '小说场景描述',
      data.priority || 'normal',
      true // includeCompleted = true
    );

    return {
      success: true,
      taskId: result.task.id,
      message: `已启动${result.actionType}所有提示词，处理 ${result.segmentCount} 个段落`,
    };
  },
  {
    auth: true,
    schema: GeneratePromptsSchema,
  },
);

/**
 * 重新生成所有图片（包括已完成的段落）
 * Re-generate images for all segments (including completed segments)
 */
export const regenerateAllImagesAction = enhanceAction(
  async function (data: GenerateImagesInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createImageGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.priority || 'normal',
      true // includeCompleted = true
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动${result.actionType}所有图片，处理 ${result.segmentCount} 个段落（${result.imageSettings.width}x${result.imageSettings.height}），使用配置：${result.configName}`,
      imageSettings: result.imageSettings,
    };
  },
  {
    auth: true,
    schema: GenerateImagesSchema,
  },
);