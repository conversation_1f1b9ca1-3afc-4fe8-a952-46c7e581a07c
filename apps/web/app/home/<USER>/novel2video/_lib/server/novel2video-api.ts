import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database';

type Novel2VideoProject =
  Database['public']['Tables']['novel2video_projects']['Row'];
type Novel2VideoSegment =
  Database['public']['Tables']['novel2video_segments']['Row'];
type Novel2VideoAudioFile =
  Database['public']['Tables']['novel2video_audio_files']['Row'];
type Novel2VideoImageFile =
  Database['public']['Tables']['novel2video_image_files']['Row'];
type Novel2VideoVideo =
  Database['public']['Tables']['novel2video_videos']['Row'];
type Novel2VideoCharacter =
  Database['public']['Tables']['novel2video_characters']['Row'];
type Novel2VideoSegmentCharacter =
  Database['public']['Tables']['novel2video_segment_characters']['Row'];

type CreateProjectData = {
  title: string;
  description?: string;
  originalText: string;
  accountId: string;
  userId: string;
  settings?: Record<string, unknown>;
};

type UpdateProjectData = {
  title?: string;
  description?: string;
  original_text?: string;
  status?: Database['public']['Enums']['novel2video_project_status'];
  progress?: number;
  settings?: Record<string, unknown>;
};

/**
 * Novel2Video API service for database operations
 */
export class Novel2VideoApi {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * 创建新项目
   * Create a new project
   */
  async createProject(data: CreateProjectData): Promise<Novel2VideoProject> {
    const { data: project, error } = await this.client
      .from('novel2video_projects')
      .insert({
        title: data.title,
        description: data.description,
        original_text: data.originalText,
        account_id: data.accountId,
        created_by: data.userId,
        updated_by: data.userId,
        settings: (data.settings || {}) as any,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return project;
  }

  /**
   * 获取账户的所有项目
   * Get all projects for an account
   */
  async getProjects(accountId: string): Promise<Novel2VideoProject[]> {
    const { data: projects, error } = await this.client
      .from('novel2video_projects')
      .select('*')
      .eq('account_id', accountId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return projects || [];
  }

  /**
   * 根据ID获取项目
   * Get project by ID
   */
  async getProject(projectId: string): Promise<Novel2VideoProject | null> {
    const { data: project, error } = await this.client
      .from('novel2video_projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw error;
    }

    return project;
  }

  /**
   * 更新项目
   * Update project
   */
  async updateProject(
    projectId: string,
    data: UpdateProjectData,
    userId: string,
  ): Promise<Novel2VideoProject> {
    const { data: project, error } = await this.client
      .from('novel2video_projects')
      .update({
        ...data,
        settings: data.settings as any,
        updated_by: userId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', projectId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return project;
  }

  /**
   * 删除项目
   * Delete project
   */
  async deleteProject(projectId: string): Promise<void> {
    const { error } = await this.client
      .from('novel2video_projects')
      .delete()
      .eq('id', projectId);

    if (error) {
      throw error;
    }
  }

  /**
   * 创建文本片段
   * Create text segments for a project
   */
  async createSegments(
    projectId: string,
    segments: Array<{ index: number; text: string }>,
  ): Promise<Novel2VideoSegment[]> {
    const segmentData = segments.map((segment) => ({
      project_id: projectId,
      segment_index: segment.index,
      text_content: segment.text,
    }));

    const { data: createdSegments, error } = await this.client
      .from('novel2video_segments')
      .insert(segmentData)
      .select();

    if (error) {
      throw error;
    }

    return createdSegments || [];
  }

  /**
   * 获取项目的所有片段
   * Get all segments for a project
   */
  async getSegments(projectId: string): Promise<Novel2VideoSegment[]> {
    const { data: segments, error } = await this.client
      .from('novel2video_segments')
      .select(`
        *,
        image_prompt,
        prompt_generated_at,
        prompt_generation_task_id
      `)
      .eq('project_id', projectId)
      .order('segment_index', { ascending: true });

    if (error) {
      throw error;
    }

    return segments || [];
  }

  /**
   * 更新片段状态
   * Update segment status
   */
  async updateSegmentStatus(
    segmentId: string,
    status: Database['public']['Enums']['novel2video_segment_status'],
    durationSeconds?: number,
  ): Promise<Novel2VideoSegment> {
    const updateData: any = { status };
    if (durationSeconds !== undefined) {
      updateData.duration_seconds = durationSeconds;
    }

    const { data: segment, error } = await this.client
      .from('novel2video_segments')
      .update(updateData)
      .eq('id', segmentId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return segment;
  }

  /**
   * 更新片段内容
   * Update segment content
   */
  async updateSegment(
    segmentId: string,
    data: {
      text_content?: string;
      status?: Database['public']['Enums']['novel2video_segment_status'];
    },
  ): Promise<Novel2VideoSegment> {
    const { data: segment, error } = await this.client
      .from('novel2video_segments')
      .update(data)
      .eq('id', segmentId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return segment;
  }

  /**
   * 删除单个片段
   * Delete a single segment
   */
  async deleteSegment(segmentId: string): Promise<void> {
    const { error } = await this.client
      .from('novel2video_segments')
      .delete()
      .eq('id', segmentId);

    if (error) {
      throw error;
    }
  }

  /**
   * 删除项目的所有片段
   * Delete all segments for a project
   */
  async deleteAllSegments(projectId: string): Promise<void> {
    const { error } = await this.client
      .from('novel2video_segments')
      .delete()
      .eq('project_id', projectId);

    if (error) {
      throw error;
    }
  }

  /**
   * 创建音频文件记录
   * Create audio file record
   */
  async createAudioFile(data: {
    segmentId: string;
    filePath: string;
    fileSize?: number;
    durationSeconds?: number;
    voiceSettings?: Record<string, unknown>;
  }): Promise<Novel2VideoAudioFile> {
    const { data: audioFile, error } = await this.client
      .from('novel2video_audio_files')
      .insert({
        segment_id: data.segmentId,
        file_path: data.filePath,
        file_size: data.fileSize,
        duration_seconds: data.durationSeconds,
        voice_settings: (data.voiceSettings || {}) as any,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return audioFile;
  }

  /**
   * 创建图片文件记录
   * Create image file record
   */
  async createImageFile(data: {
    segmentId: string;
    filePath: string;
    fileSize?: number;
    width?: number;
    height?: number;
    imagePrompt?: string;
    generationSettings?: Record<string, unknown>;
  }): Promise<Novel2VideoImageFile> {
    const { data: imageFile, error } = await this.client
      .from('novel2video_image_files')
      .insert({
        segment_id: data.segmentId,
        file_path: data.filePath,
        file_size: data.fileSize,
        width: data.width,
        height: data.height,
        image_prompt: data.imagePrompt,
        generation_settings: (data.generationSettings || {}) as any,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return imageFile;
  }

  /**
   * 创建最终视频记录
   * Create final video record
   */
  async createVideo(data: {
    projectId: string;
    filePath: string;
    fileSize?: number;
    durationSeconds?: number;
    width?: number;
    height?: number;
    frameRate?: number;
    videoSettings?: Record<string, unknown>;
  }): Promise<Novel2VideoVideo> {
    const { data: video, error } = await this.client
      .from('novel2video_videos')
      .insert({
        project_id: data.projectId,
        file_path: data.filePath,
        file_size: data.fileSize,
        duration_seconds: data.durationSeconds,
        width: data.width,
        height: data.height,
        frame_rate: data.frameRate,
        video_settings: (data.videoSettings || {}) as any,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return video;
  }

  /**
   * 获取项目完整信息（包含片段、音频、图片文件）
   * Get complete project information with segments, audio, and image files
   */
  async getProjectWithDetails(projectId: string) {
    // 获取项目基本信息
    const project = await this.getProject(projectId);
    if (!project) {
      return null;
    }

    // 获取片段
    const segments = await this.getSegments(projectId);

    // 获取每个片段的音频和图片文件
    const segmentDetails = await Promise.all(
      segments.map(async (segment) => {
        const [audioFiles, imageFiles] = await Promise.all([
          this.client
            .from('novel2video_audio_files')
            .select('*')
            .eq('segment_id', segment.id)
            .order('created_at', { ascending: false }),
          this.client
            .from('novel2video_image_files')
            .select('*')
            .eq('segment_id', segment.id)
            .order('created_at', { ascending: false }),
        ]);

        return {
          ...segment,
          audioFile: audioFiles.data?.[0] || null,
          imageFile: imageFiles.data?.[0] || null,
        };
      }),
    );

    // 获取最终视频
    const { data: video } = await this.client
      .from('novel2video_videos')
      .select('*')
      .eq('project_id', projectId)
      .single();

    return {
      project,
      segments: segmentDetails,
      video: video || null,
    };
  }

  /**
   * 创建项目人物
   * Create project character
   */
  async createCharacter(data: {
    projectId: string;
    name: string;
    aliases?: string[];
    imagePrompt?: string;
  }): Promise<Novel2VideoCharacter> {
    const { data: character, error } = await this.client
      .from('novel2video_characters')
      .insert({
        project_id: data.projectId,
        name: data.name,
        aliases: data.aliases || [],
        image_prompt: data.imagePrompt,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return character;
  }

  /**
   * 批量创建项目人物
   * Batch create project characters
   */
  async createCharacters(
    projectId: string,
    characters: Array<{ name: string; aliases?: string[]; imagePrompt?: string }>
  ): Promise<Novel2VideoCharacter[]> {
    const { data: createdCharacters, error } = await this.client
      .from('novel2video_characters')
      .insert(
        characters.map((char) => ({
          project_id: projectId,
          name: char.name,
          aliases: char.aliases || [],
          image_prompt: char.imagePrompt,
        }))
      )
      .select();

    if (error) {
      throw error;
    }

    return createdCharacters || [];
  }

  /**
   * 获取项目所有人物
   * Get all characters for a project
   */
  async getCharacters(projectId: string): Promise<Novel2VideoCharacter[]> {
    const { data: characters, error } = await this.client
      .from('novel2video_characters')
      .select('*')
      .eq('project_id', projectId)
      .order('name');

    if (error) {
      throw error;
    }

    return characters || [];
  }

  /**
   * 更新人物信息
   * Update character information
   */
  async updateCharacter(
    characterId: string,
    data: {
      name?: string;
      aliases?: string[];
      imagePrompt?: string;
    }
  ): Promise<Novel2VideoCharacter> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.aliases !== undefined) updateData.aliases = data.aliases;
    if (data.imagePrompt !== undefined) updateData.image_prompt = data.imagePrompt;

    const { data: character, error } = await this.client
      .from('novel2video_characters')
      .update(updateData)
      .eq('id', characterId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return character;
  }

  /**
   * 删除人物
   * Delete character
   */
  async deleteCharacter(characterId: string): Promise<void> {
    const { error } = await this.client
      .from('novel2video_characters')
      .delete()
      .eq('id', characterId);

    if (error) {
      throw error;
    }
  }

  /**
   * 创建段落人物关联
   * Create segment character association
   */
  async createSegmentCharacter(data: {
    segmentId: string;
    characterId: string;
  }): Promise<Novel2VideoSegmentCharacter> {
    const { data: segmentCharacter, error } = await this.client
      .from('novel2video_segment_characters')
      .insert({
        segment_id: data.segmentId,
        character_id: data.characterId,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return segmentCharacter;
  }

  /**
   * 批量创建段落人物关联
   * Batch create segment character associations
   */
  async createSegmentCharacters(
    segmentId: string,
    characterIds: string[]
  ): Promise<Novel2VideoSegmentCharacter[]> {
    const { data: segmentCharacters, error } = await this.client
      .from('novel2video_segment_characters')
      .insert(
        characterIds.map((characterId) => ({
          segment_id: segmentId,
          character_id: characterId,
        }))
      )
      .select();

    if (error) {
      throw error;
    }

    return segmentCharacters || [];
  }

  /**
   * 获取段落关联的人物
   * Get characters associated with a segment
   */
  async getSegmentCharacters(segmentId: string): Promise<{
    segmentCharacter: Novel2VideoSegmentCharacter;
    character: Novel2VideoCharacter;
  }[]> {
    const { data: segmentCharacters, error } = await this.client
      .from('novel2video_segment_characters')
      .select(`
        *,
        character:character_id (*)
      `)
      .eq('segment_id', segmentId);

    if (error) {
      throw error;
    }

    return (segmentCharacters || []).map((sc: any) => ({
      segmentCharacter: sc,
      character: sc.character,
    }));
  }

  /**
   * 删除段落人物关联
   * Remove segment character association
   */
  async removeSegmentCharacter(
    segmentId: string,
    characterId: string
  ): Promise<void> {
    const { error } = await this.client
      .from('novel2video_segment_characters')
      .delete()
      .eq('segment_id', segmentId)
      .eq('character_id', characterId);

    if (error) {
      throw error;
    }
  }

  /**
   * 清除段落的所有人物关联
   * Clear all character associations for a segment
   */
  async clearSegmentCharacters(segmentId: string): Promise<void> {
    const { error } = await this.client
      .from('novel2video_segment_characters')
      .delete()
      .eq('segment_id', segmentId);

    if (error) {
      throw error;
    }
  }

  /**
   * 获取项目完整信息（包含人物信息）
   * Get complete project information with character data
   */
  async getProjectWithCharacters(projectId: string) {
    const projectDetails = await this.getProjectWithDetails(projectId);
    if (!projectDetails) {
      return null;
    }

    // 获取项目所有人物
    const characters = await this.getCharacters(projectId);

    // 为每个段落获取关联的人物
    const segmentsWithCharacters = await Promise.all(
      projectDetails.segments.map(async (segmentDetail) => {
        const segmentCharacters = await this.getSegmentCharacters(
          segmentDetail.id
        );
        return {
          ...segmentDetail,
          characters: segmentCharacters.map((sc) => sc.character),
        };
      })
    );

    return {
      ...projectDetails,
      segments: segmentsWithCharacters,
      characters,
    };
  }
}

export function createNovel2VideoApi(client: SupabaseClient<Database>) {
  return new Novel2VideoApi(client);
}
