/**
 * Novel2Video Server Actions
 * 
 * This file serves as the main entry point for all Novel2Video server actions.
 * All actions have been refactored into separate modules for better maintainability.
 * 
 * @see ./actions/ - Individual action modules
 * @see ./types/server-action-schemas.ts - Type definitions and validation schemas
 * @see ./utils/ - Utility functions
 */

// Re-export all actions from their respective modules
export * from './actions';

// Legacy exports for backward compatibility
// These can be removed once all imports are updated
export {
  // Project management
  createProjectAction,
  getProjectsAction,
  getProjectDetailsAction,
  updateProjectAction,
  deleteProjectAction,
  startProcessingAction,
  pauseProcessingAction,
  
  // Segment management
  updateSegmentAction,
  deleteSegmentAction,
  resegmentProjectAction,
  
  // Task management
  getTaskStatusAction,
  getProjectTasksAction,
  cancelTaskAction,
  retryTaskAction,
  
  // Image generation
  generateAllSegmentPromptsAction,
  generateSingleSegmentPromptAction,
  generateSingleSegmentImageAction,
  generateAllSegmentImagesAction,
  regenerateAllPromptsAction,
  regenerateAllImagesAction,
  
  // Character management
  extractCharactersAction,
  createCharacterAction,
  updateCharacterAction,
  deleteCharacterAction,
  getProjectCharactersAction,
  extractSegmentCharactersAction,
  analyzeAllSegmentCharactersAction,
  reanalyzeAllSegmentCharactersAction,
  
  // Audio generation
  generateAllSegmentAudioAction,
  generateSingleSegmentAudioAction,
  regenerateAllAudioAction,
  
  // ComfyUI configuration
  getAvailableConfigsAction,
  checkComfyUIConnectionAction,
  getWorkflowConfigAction,
} from './actions';