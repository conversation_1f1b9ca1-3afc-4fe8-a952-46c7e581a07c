'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoApi } from '../novel2video-api';
import { createNovel2VideoTaskApi } from '@kit/novel2video/api';

import {
  ExtractCharactersSchema,
  CreateCharacterSchema,
  UpdateCharacterSchema,
  DeleteCharacterSchema,
  ExtractSegmentCharactersSchema,
  AnalyzeSegmentCharactersSchema,
  type ExtractCharactersInput,
  type CreateCharacterInput,
  type UpdateCharacterInput,
  type DeleteCharacterInput,
  type ExtractSegmentCharactersInput,
  type AnalyzeSegmentCharactersInput,
} from '../../types/server-action-schemas';

/**
 * 通用的段落人物分析逻辑
 */
async function createSegmentCharacterAnalysisTasks(
  client: any,
  api: any,
  taskApi: any,
  projectId: string,
  includeCompleted: boolean = false
) {
  // 获取项目信息
  const project = await api.getProject(projectId);
  if (!project) {
    throw new Error('项目不存在');
  }

  // 检查项目是否有人物（需要先提取人物）
  const characters = await api.getCharacters(projectId);
  if (!characters || characters.length === 0) {
    throw new Error('请先提取项目人物，然后再分析段落人物');
  }

  let segmentsToProcess = [];

  if (includeCompleted) {
    // 重新分析：获取所有段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId)
      .order('segment_index', { ascending: true });

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('项目中没有找到段落');
    }

    segmentsToProcess = segments;
  } else {
    // 只分析未完成的：获取还没有人物关联的段落
    const { data: allSegments, error: allSegmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId)
      .order('segment_index', { ascending: true });

    if (allSegmentsError) {
      throw new Error(allSegmentsError.message);
    }

    if (!allSegments || allSegments.length === 0) {
      throw new Error('项目没有段落数据');
    }

    // 获取已经有人物关联的段落ID
    const { data: analyzedSegments, error: analyzedError } = await client
      .from('novel2video_segment_characters')
      .select('segment_id')
      .in('segment_id', allSegments.map((s: any) => s.id));

    if (analyzedError) {
      throw new Error(analyzedError.message);
    }

    // 找出未分析的段落
    const analyzedSegmentIds = new Set(analyzedSegments?.map((s: any) => s.segment_id) || []);
    segmentsToProcess = allSegments.filter((segment: any) => !analyzedSegmentIds.has(segment.id));

    if (segmentsToProcess.length === 0) {
      throw new Error('所有段落都已分析过人物，请使用"重新分析"按钮');
    }
  }

  // 创建任务
  const tasks = [];
  for (const segment of segmentsToProcess) {
    const task = await taskApi.createExtractSegmentCharactersTask({
      accountId: project.account_id,
      projectId: projectId,
      segmentId: segment.id,
      priority: 'normal',
    });
    tasks.push(task);
  }

  return {
    tasks,
    segmentCount: segmentsToProcess.length,
    actionType: includeCompleted ? '重新分析' : '分析'
  };
}

/**
 * 提取项目人物
 * Extract characters from project text
 */
export const extractCharactersAction = enhanceAction(
  async function (data: ExtractCharactersInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取项目信息
    const project = await api.getProject(data.projectId);
    if (!project) {
      throw new Error('项目不存在');
    }

    try {
      // 创建人物提取任务
      const task = await taskApi.createExtractCharactersTask({
        accountId: project.account_id,
        projectId: data.projectId,
        priority: 'normal',
      });

      return {
        success: true,
        taskId: task.id,
        message: '人物提取任务已创建，请等待处理完成',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '人物提取任务创建失败',
      };
    }
  },
  {
    auth: true,
    schema: ExtractCharactersSchema,
  },
);

/**
 * 创建人物
 * Create character
 */
export const createCharacterAction = enhanceAction(
  async function (data: CreateCharacterInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    try {
      const character = await api.createCharacter({
        projectId: data.projectId,
        name: data.name,
        aliases: data.aliases,
        imagePrompt: data.imagePrompt,
      });

      return {
        success: true,
        character,
        message: '人物创建成功',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '人物创建失败',
      };
    }
  },
  {
    auth: true,
    schema: CreateCharacterSchema,
  },
);

/**
 * 更新人物信息
 * Update character
 */
export const updateCharacterAction = enhanceAction(
  async function (data: UpdateCharacterInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    try {
      const character = await api.updateCharacter(data.characterId, {
        name: data.name,
        aliases: data.aliases,
        imagePrompt: data.imagePrompt,
      });

      return {
        success: true,
        character,
        message: '人物更新成功',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '人物更新失败',
      };
    }
  },
  {
    auth: true,
    schema: UpdateCharacterSchema,
  },
);

/**
 * 删除人物
 * Delete character
 */
export const deleteCharacterAction = enhanceAction(
  async function (data: DeleteCharacterInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    try {
      await api.deleteCharacter(data.characterId);

      return {
        success: true,
        message: '人物删除成功',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '人物删除失败',
      };
    }
  },
  {
    auth: true,
    schema: DeleteCharacterSchema,
  },
);

/**
 * 获取项目人物列表
 * Get project characters
 */
export const getProjectCharactersAction = enhanceAction(
  async function (data: ExtractCharactersInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    try {
      const characters = await api.getCharacters(data.projectId);

      return {
        success: true,
        characters,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取人物列表失败',
      };
    }
  },
  {
    auth: true,
    schema: ExtractCharactersSchema, // 复用同样的schema
  },
);

/**
 * 提取段落人物
 * Extract characters from segment text
 */
export const extractSegmentCharactersAction = enhanceAction(
  async function (data: ExtractSegmentCharactersInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取段落信息
    const { data: segment, error: segmentError } = await client
      .from('novel2video_segments')
      .select('*')
      .eq('id', data.segmentId)
      .single();
    
    if (segmentError || !segment) {
      throw new Error('段落不存在');
    }

    // 获取项目信息
    const project = await api.getProject(segment.project_id);
    if (!project) {
      throw new Error('项目不存在');
    }

    try {
      // 先删除该段落已有的人物关联数据
      const { error: deleteError } = await client
        .from('novel2video_segment_characters')
        .delete()
        .eq('segment_id', data.segmentId);

      if (deleteError) {
        console.warn('删除段落人物关联时出错:', deleteError);
        // 不抛出错误，继续执行分析任务
      }

      // 创建段落人物提取任务
      const task = await taskApi.createExtractSegmentCharactersTask({
        accountId: project.account_id,
        projectId: segment.project_id,
        segmentId: data.segmentId,
        priority: 'normal',
      });

      return {
        success: true,
        taskId: task.id,
        message: '已清除旧数据并创建段落人物分析任务，请等待处理完成',
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '段落人物分析任务创建失败',
      };
    }
  },
  {
    auth: true,
    schema: ExtractSegmentCharactersSchema,
  },
);

/**
 * 批量分析所有段落的人物（仅处理未完成的段落）
 * Batch analyze characters for all segments (only process unfinished segments)
 */
export const analyzeAllSegmentCharactersAction = enhanceAction(
  async function (data: AnalyzeSegmentCharactersInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createSegmentCharacterAnalysisTasks(
      client, api, taskApi, data.projectId, false // includeCompleted = false
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动批量段落人物${result.actionType}，处理 ${result.segmentCount} 个段落`,
    };
  },
  {
    auth: true,
    schema: AnalyzeSegmentCharactersSchema,
  },
);

/**
 * 重新分析所有段落的人物（包括已完成的段落）
 * Re-analyze characters for all segments (including completed segments)
 */
export const reanalyzeAllSegmentCharactersAction = enhanceAction(
  async function (data: AnalyzeSegmentCharactersInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createSegmentCharacterAnalysisTasks(
      client, api, taskApi, data.projectId, true // includeCompleted = true
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动${result.actionType}所有段落人物，处理 ${result.segmentCount} 个段落`,
    };
  },
  {
    auth: true,
    schema: AnalyzeSegmentCharactersSchema,
  },
);