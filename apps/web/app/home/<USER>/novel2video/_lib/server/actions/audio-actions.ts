'use server';

import { z } from 'zod';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoTaskApi } from '@kit/novel2video/api';

import {
  GenerateAudioSchema,
  GenerateSingleSegmentAudioSchema,
  RegenerateAllAudioSchema,
  type GenerateAudioInput,
  type GenerateSingleSegmentAudioInput,
  type RegenerateAllAudioInput,
} from '../../types/server-action-schemas';

/**
 * 通用的音频生成逻辑
 */
async function createAudioGenerationTasks(
  client: any,
  taskApi: any,
  projectId: string,
  priority: string,
  includeCompleted: boolean = false
) {
  // 获取项目信息和权限检查
  const { data: project, error: projectError } = await client
    .from('novel2video_projects')
    .select('account_id, settings')
    .eq('id', projectId)
    .single();

  if (projectError || !project) {
    throw new Error('项目不存在');
  }

  let segmentsToProcess = [];

  if (includeCompleted) {
    // 重新生成：查询所有段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select('id')
      .eq('project_id', projectId);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('项目中没有找到段落');
    }

    segmentsToProcess = segments;
  } else {
    // 只生成未完成的：查询没有音频文件的段落
    const { data: segments, error: segmentsError } = await client
      .from('novel2video_segments')
      .select(`
        id,
        audioFile:novel2video_audio_files(id)
      `)
      .eq('project_id', projectId);

    if (segmentsError) {
      throw new Error(segmentsError.message);
    }

    if (!segments || segments.length === 0) {
      throw new Error('项目中没有找到段落');
    }

    // 过滤出没有音频文件的段落
    segmentsToProcess = segments.filter((s: any) => !s.audioFile);

    if (segmentsToProcess.length === 0) {
      throw new Error('没有待处理的段落（所有段落都已生成音频）');
    }
  }

  // 获取项目设置中的音频配置
  let audioSettings = {
    workflowConfigId: null as string | null
  };

  try {
    if (project.settings && typeof project.settings === 'object') {
      const settings = project.settings as any;
      if (settings.audio) {
        audioSettings = { ...audioSettings, ...settings.audio };
      }
    }
  } catch (error) {
    console.warn('Failed to parse project settings, using defaults:', error);
  }

  // 从项目设置中获取音频工作流配置ID
  const configId = audioSettings.workflowConfigId;
  
  if (!configId) {
    throw new Error('请先在项目设置中配置音频生成工作流');
  }

  // 验证项目配置的工作流是否仍然可用
  const { data: config, error: configError } = await client
    .from('comfyui_workflow_configs')
    .select('id, config_name, display_name')
    .eq('id', configId)
    .eq('status', 'active')
    .single();

  if (configError || !config) {
    throw new Error('项目配置的音频工作流已不可用，请在项目设置中重新选择');
  }
  
  const configName = config.display_name || config.config_name;

  // 为每个段落创建音频生成任务
  const tasks = [];
  for (const segment of segmentsToProcess) {
    const task = await taskApi.createGenerateAudioTask({
      accountId: project.account_id,
      projectId: projectId,
      segmentId: segment.id,
      configId: configId,
      parameters: {},
      priority: priority,
    });
    tasks.push(task);
  }

  return {
    tasks,
    segmentCount: segmentsToProcess.length,
    configName,
    actionType: includeCompleted ? '重新生成' : '生成',
  };
}

/**
 * 为所有待处理段落生成音频（批量操作）
 * Generate audio for all pending segments (batch operation)
 */
export const generateAllSegmentAudioAction = enhanceAction(
  async function (data: GenerateAudioInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createAudioGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.priority || 'normal',
      false // includeCompleted = false
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动音频${result.actionType}任务，处理 ${result.segmentCount} 个段落，使用配置：${result.configName}`,
      configName: result.configName,
    };
  },
  {
    auth: true,
    schema: GenerateAudioSchema,
  },
);

/**
 * 为单个段落生成音频
 * Generate audio for a single segment
 */
export const generateSingleSegmentAudioAction = enhanceAction(
  async function (data: GenerateSingleSegmentAudioInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取段落信息
    const { data: segment, error: segmentError } = await client
      .from('novel2video_segments')
      .select('id, project_id, text_content')
      .eq('id', data.segmentId)
      .single();

    if (segmentError || !segment) {
      throw new Error('段落不存在');
    }

    // 获取项目信息和设置
    const { data: project, error: projectError } = await client
      .from('novel2video_projects')
      .select('account_id, settings')
      .eq('id', segment.project_id)
      .single();

    if (projectError || !project) {
      throw new Error('项目不存在');
    }

    // 获取项目设置中的音频配置
    let audioSettings = {
      workflowConfigId: null as string | null
    };

    try {
      if (project.settings && typeof project.settings === 'object') {
        const settings = project.settings as any;
        if (settings.audio) {
          audioSettings = { ...audioSettings, ...settings.audio };
        }
      }
    } catch (error) {
      console.warn('Failed to parse project settings, using defaults:', error);
    }

    // 从项目设置中获取音频工作流配置ID
    const configId = audioSettings.workflowConfigId;
    
    if (!configId) {
      throw new Error('请先在项目设置中配置音频生成工作流');
    }

    // 验证项目配置的工作流是否仍然可用
    const { data: config, error: configError } = await client
      .from('comfyui_workflow_configs')
      .select('id, config_name, display_name')
      .eq('id', configId)
      .eq('status', 'active')
      .single();

    if (configError || !config) {
      throw new Error('项目配置的音频工作流已不可用，请在项目设置中重新选择');
    }
    
    const configName = config.display_name || config.config_name;

    // 创建音频生成任务
    const task = await taskApi.createGenerateAudioTask({
      accountId: project.account_id,
      projectId: segment.project_id,
      segmentId: segment.id,
      configId: configId,
      parameters: {},
      priority: data.priority || 'normal',
    });

    return {
      success: true,
      taskId: task.id,
      message: `已启动单个段落的音频生成任务，使用配置：${configName}`,
      configName,
    };
  },
  {
    auth: true,
    schema: GenerateSingleSegmentAudioSchema,
  },
);

/**
 * 重新生成所有音频（包括已完成的段落）
 * Re-generate audio for all segments (including completed segments)
 */
export const regenerateAllAudioAction = enhanceAction(
  async function (data: RegenerateAllAudioInput, user) {
    const client = getSupabaseServerClient();
    const taskApi = createNovel2VideoTaskApi(client);

    const result = await createAudioGenerationTasks(
      client,
      taskApi,
      data.projectId,
      data.priority || 'normal',
      true // includeCompleted = true
    );

    return {
      success: true,
      taskIds: result.tasks.map(t => t.id),
      message: `已启动${result.actionType}所有音频，处理 ${result.segmentCount} 个段落，使用配置：${result.configName}`,
      configName: result.configName,
    };
  },
  {
    auth: true,
    schema: RegenerateAllAudioSchema,
  },
);