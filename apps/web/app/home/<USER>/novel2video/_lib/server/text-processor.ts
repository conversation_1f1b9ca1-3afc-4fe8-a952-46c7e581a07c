/**
 * 文本处理工具
 * Text processing utilities for Novel2Video
 */

/**
 * 将长文本分割成适合生成场景的片段。
 * 这个阶段的分割粒度较细,先按段落分,再按句子分。
 *
 * Split long text into segments suitable for scene generation.
 * The splitting at this stage is fine-grained, first by paragraphs then by sentences.
 */
export function splitTextIntoSegments(text: string): string[] {
  const preprocessedText = preprocessText(text);

  if (!preprocessedText) {
    return [];
  }

  // 先按段落分割
  const paragraphs = preprocessedText
    .split(/\n+/)
    .filter((p) => p.trim().length > 0);

  // 对每个段落再按句子分割
  const segments = paragraphs.flatMap((paragraph) => {
    return splitIntoSentencesSimple(paragraph);
  });

  return postProcessSegments(segments);
}

/**
 * 简单的句子分割函数，仅根据主要结束标点分割。
 * Simple sentence splitter based on major ending punctuation.
 */
function splitIntoSentencesSimple(text: string): string[] {
  if (!text) {
    return [];
  }

  // 正则表达式匹配句子结束标点，并保留它们
  const sentenceEndRegex = /([。！？\.!?]+)/g;
  const parts = text.split(sentenceEndRegex);
  const sentences: string[] = [];

  for (let i = 0; i < parts.length; i += 2) {
    const sentencePart = parts[i];
    const punctuation = parts[i + 1] || '';

    if (sentencePart && sentencePart.trim()) {
      sentences.push((sentencePart + punctuation).trim());
    }
  }

  // 如果没有找到分割点，返回整个文本
  return sentences.length > 0 ? sentences : [text.trim()];
}

/**
 * 预处理文本：去掉引号，清理格式
 * Preprocess text: remove quotes, clean formatting
 */
function preprocessText(text: string): string {
  return (
    text
      .trim()
      // 去掉所有类型的引号
      .replace(/["""'''「」『』]/g, '')
      // 统一换行符
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // 清理多余的空白字符，但保留换行
      .replace(/[ \t]+/g, ' ')
      // 移除连续的空行，但保留段落分隔
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      // 去掉行首行尾的空格
      .split('\n')
      .map((line) => line.trim())
      .join('\n')
      .trim()
  );
}

/**
 * 后处理片段：过滤无效片段，优化结果
 * Post-process segments: filter invalid segments, optimize results
 */
function postProcessSegments(segments: string[]): string[] {
  return (
    segments
      .map((segment) => segment.trim())
      .filter((segment) => segment.length > 0)
      // 过滤仅包含标点符号的片段
      .filter((segment) => !isOnlyPunctuation(segment))
      // 过滤太短的无意义片段（少于2个字符）
      .filter((segment) => segment.length >= 2)
      // 过滤仅包含数字的片段（通常是页码等）
      .filter((segment) => !/^\d+$/.test(segment))
      // 去重（保持顺序）
      .filter((segment, index, array) => array.indexOf(segment) === index)
  );
}

/**
 * 检查文本是否仅包含标点符号
 * Check if text contains only punctuation marks
 */
function isOnlyPunctuation(text: string): boolean {
  // 扩展的标点符号正则，包括中英文标点
  const punctuationOnlyRegex =
    /^[，。！？,;:.!?、："""'''「」『』（）\(\)【】\[\]…—－\-\s]+$/;
  return punctuationOnlyRegex.test(text);
}

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

/**
 * 生成图片提示词 - 针对短视频优化
 * Generate image prompt for a text segment - optimized for short videos
 */
export async function generateImagePrompt(
  textSegment: string,
  style = 'realistic',
): Promise<string> {
  const i18n = await createI18nServerInstance();
  
  // 移除标点符号，提取关键词
  const cleanText = textSegment.replace(/[。！？，,;；:.!?]/g, ' ');
  const words = cleanText.split(/\s+/).filter((word) => word.length > 1);

  // 针对短视频的基础提示词模板
  const basePrompts = {
    realistic: i18n.t('novel2video:prompts.styles.realistic'),
    artistic: i18n.t('novel2video:prompts.styles.artistic'),
    anime: i18n.t('novel2video:prompts.styles.anime'),
    cartoon: i18n.t('novel2video:prompts.styles.cartoon'),
    cinematic: i18n.t('novel2video:prompts.styles.cinematic'),
    watercolor: i18n.t('novel2video:prompts.styles.watercolor'),
  };

  const basePrompt =
    basePrompts[style as keyof typeof basePrompts] || basePrompts.realistic;

  // 为短视频优化的提示词特征
  const shortVideoFeatures = [
    i18n.t('novel2video:prompts.features.verticalComposition'),
    i18n.t('novel2video:prompts.features.mobileOptimized'),
    i18n.t('novel2video:prompts.features.highContrast'),
    i18n.t('novel2video:prompts.features.clearFocus'),
    i18n.t('novel2video:prompts.features.dramaticLighting'),
  ];

  // 检测情感和场景类型
  let emotionPrompt = '';
  if (
    textSegment.includes('哭') ||
    textSegment.includes('泪') ||
    textSegment.includes('悲')
  ) {
    emotionPrompt = i18n.t('novel2video:prompts.emotions.sad');
  } else if (
    textSegment.includes('笑') ||
    textSegment.includes('乐') ||
    textSegment.includes('喜')
  ) {
    emotionPrompt = i18n.t('novel2video:prompts.emotions.happy');
  } else if (
    textSegment.includes('怒') ||
    textSegment.includes('气') ||
    textSegment.includes('愤')
  ) {
    emotionPrompt = i18n.t('novel2video:prompts.emotions.angry');
  }

  // 限制文本长度，取前50个字符
  const shortText =
    textSegment.length > 50
      ? textSegment.substring(0, 50) + '...'
      : textSegment;

  return `${basePrompt} ${shortText}${emotionPrompt}, ${shortVideoFeatures.join(', ')}, ${i18n.t('novel2video:prompts.quality')}`;
}

/**
 * 估算文本的TTS音频时长（秒）- 针对字幕片段优化
 * Estimate TTS audio duration for text in seconds - optimized for subtitle segments
 */
export function estimateAudioDuration(
  text: string,
  wordsPerMinute = 180,
): number {
  // 中文按字符计算，英文按单词计算
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = text
    .replace(/[\u4e00-\u9fff]/g, '')
    .split(/\s+/)
    .filter((w) => w.length > 0).length;

  // 针对字幕调整语速：中文大约每分钟350字，英文大约每分钟180词
  const chineseMinutes = chineseChars / 350;
  const englishMinutes = englishWords / wordsPerMinute;

  const totalMinutes = chineseMinutes + englishMinutes;

  // 字幕片段最少0.5秒（很短的字也要有时间显示），最多不限制
  const duration = totalMinutes * 60;
  return Math.max(0.5, Math.round(duration * 10) / 10); // 保留一位小数
}

/**
 * 清理和格式化文本用于TTS - 字幕优化
 * Clean and format text for TTS - optimized for subtitles
 */
export function formatTextForTTS(text: string): string {
  return (
    text
      .trim()
      // 统一标点符号，增加适当停顿
      .replace(/[。！？]/g, '。')
      .replace(/[，；]/g, '，')
      // 移除多余的空格
      .replace(/\s+/g, ' ')
      // 为字幕增加适当的停顿
      .replace(/，/g, '，') // 短停顿
      .replace(/。/g, '。') // 长停顿
      // 处理引号，增加语调变化
      .replace(/"/g, '"')
      .replace(/"/g, '"')
  );
}
