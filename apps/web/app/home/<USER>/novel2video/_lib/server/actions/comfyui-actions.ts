'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import {
  GetConfigsSchema,
  ConfigIdSchema,
  EmptySchema,
  type GetConfigsInput,
  type ConfigIdInput,
} from '../../types/server-action-schemas';

/**
 * 获取可用的工作流配置
 */
export const getAvailableConfigsAction = enhanceAction(
  async function (data: GetConfigsInput, user) {
    const client = getSupabaseServerClient();
    
    try {
      // 如果指定了工作流类型，先查询符合条件的工作流
      let workflowFilter = '';
      if (data.workflow_type) {
        const workflowQuery = await client
          .from('comfyui_workflows')
          .select('id')
          .eq('workflow_type', data.workflow_type);
        
        if (workflowQuery.error) {
          throw workflowQuery.error;
        }
        
        const workflowIds = workflowQuery.data?.map(w => w.id) || [];
        if (workflowIds.length === 0) {
          // 没有找到指定类型的工作流，返回空结果
          return {
            success: true,
            configs: [],
          };
        }
        
        workflowFilter = workflowIds.join(',');
      }

      let query = client
        .from('comfyui_workflow_configs')
        .select(`
          *,
          workflow:workflow_id (
            id,
            name,
            display_name,
            workflow_type
          )
        `)
        .eq('status', 'active')
        .eq('is_public', true)
        .order('display_order', { ascending: true });
      
      if (workflowFilter) {
        query = query.in('workflow_id', workflowFilter.split(','));
      }
      
      const { data: configs, error } = await query;
      
      if (error) {
        throw error;
      }
      
      return {
        success: true,
        configs: configs || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取配置失败',
      };
    }
  },
  {
    auth: true,
    schema: GetConfigsSchema,
  },
);

/**
 * 检查 ComfyUI 连接状态
 */
export const checkComfyUIConnectionAction = enhanceAction(
  async function () {
    const client = getSupabaseServerClient();
    
    try {
      // 获取活跃的后端数量
      const { data: backends, error: backendError } = await client
        .from('comfyui_backends')
        .select('id, endpoint_url, status')
        .eq('status', 'active');
      
      if (backendError) {
        throw backendError;
      }
      
      // 获取活跃的配置数量
      const { data: configs, error: configError } = await client
        .from('comfyui_workflow_configs')
        .select('id')
        .eq('status', 'active');
      
      if (configError) {
        throw configError;
      }
      
      return {
        success: true,
        isAvailable: (backends?.length || 0) > 0,
        activeBackends: backends?.length || 0,
        activeConfigs: configs?.length || 0,
        backends: backends?.map(b => ({
          url: b.endpoint_url,
          status: b.status
        })) || []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接检查失败',
        isAvailable: false,
        activeBackends: 0,
        activeConfigs: 0,
        backends: []
      };
    }
  },
  {
    auth: true,
  },
);

/**
 * 获取工作流配置详情
 */
export const getWorkflowConfigAction = enhanceAction(
  async function (data: ConfigIdInput, user) {
    const client = getSupabaseServerClient();
    
    try {
      const { data: config, error } = await client
        .from('comfyui_workflow_configs')
        .select(`
          *,
          workflow:workflow_id (
            id,
            name,
            display_name,
            workflow_type,
            workflow_json
          )
        `)
        .eq('id', data.configId)
        .eq('status', 'active')
        .single();
      
      if (error) {
        throw error;
      }
      
      if (!config) {
        return {
          success: false,
          error: '配置未找到',
        };
      }
      
      return {
        success: true,
        config,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取配置失败',
      };
    }
  },
  {
    auth: true,
    schema: ConfigIdSchema,
  },
);