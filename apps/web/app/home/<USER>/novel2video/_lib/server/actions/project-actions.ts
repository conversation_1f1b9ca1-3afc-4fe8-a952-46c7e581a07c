'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createNovel2VideoApi } from '../novel2video-api';
import { splitTextIntoSegments } from '../text-processor';
import { createNovel2VideoTaskApi } from '@kit/novel2video/api';

import {
  CreateProjectSchema,
  UpdateProjectSchema,
  DeleteProjectSchema,
  StartProcessingSchema,
  PauseProcessingSchema,
  ProjectIdSchema,
  EmptySchema,
  type CreateProjectInput,
  type UpdateProjectInput,
  type DeleteProjectInput,
  type StartProcessingInput,
  type PauseProcessingInput,
  type ProjectIdInput,
} from '../../types/server-action-schemas';

/**
 * 创建新的Novel2Video项目
 * Create a new Novel2Video project
 */
export const createProjectAction = enhanceAction(
  async function (data: CreateProjectInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    // 创建项目
    const project = await api.createProject({
      title: data.title,
      description: data.description,
      originalText: data.text,
      accountId: user.id,
      userId: user.id,
    });

    // 将文本分段
    const segments = splitTextIntoSegments(data.text);

    // 创建文本片段
    if (segments.length > 0) {
      await api.createSegments(
        project.id,
        segments.map((text, index) => ({ index, text })),
      );
    }

    return {
      success: true,
      data: project,
      message: '项目创建成功！',
    };
  },
  {
    auth: true,
    schema: CreateProjectSchema,
  },
);

/**
 * 获取账户的所有项目
 * Get all projects for an account
 */
export const getProjectsAction = enhanceAction(
  async function (data, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    const projects = await api.getProjects(user.id);

    return {
      success: true,
      data: projects,
    };
  },
  {
    auth: true,
    schema: EmptySchema,
  },
);

/**
 * 获取项目详情（包含片段信息和人物信息）
 * Get project details with segments and characters
 */
export const getProjectDetailsAction = enhanceAction(
  async function (data: ProjectIdInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    const projectDetails = await api.getProjectWithCharacters(data.projectId);

    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    return {
      success: true,
      data: projectDetails,
    };
  },
  {
    auth: true,
    schema: ProjectIdSchema,
  },
);

/**
 * 更新项目信息
 * Update project information
 */
export const updateProjectAction = enhanceAction(
  async function (data: UpdateProjectInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    const { projectId, ...updateData } = data;

    const project = await api.updateProject(projectId, updateData, user.id);

    return {
      success: true,
      data: project,
      message: '项目更新成功！',
    };
  },
  {
    auth: true,
    schema: UpdateProjectSchema,
  },
);

/**
 * 删除项目
 * Delete project
 */
export const deleteProjectAction = enhanceAction(
  async function (data: DeleteProjectInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    await api.deleteProject(data.projectId);

    return {
      success: true,
      message: '项目删除成功！',
    };
  },
  {
    auth: true,
    schema: DeleteProjectSchema,
  },
);

/**
 * 开始处理项目（生成音频和图片）
 * Start processing project (generate audio and images)
 */
export const startProcessingAction = enhanceAction(
  async function (data: StartProcessingInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);
    const taskApi = createNovel2VideoTaskApi(client);

    // 获取项目信息和所有段落
    const projectDetails = await api.getProjectWithDetails(data.projectId);
    if (!projectDetails) {
      throw new Error('项目不存在');
    }

    if (projectDetails.segments.length === 0) {
      throw new Error('项目没有文本段落，请先进行文本切分');
    }

    // 更新项目状态为处理中
    await api.updateProject(
      data.projectId,
      {
        status: 'processing',
        progress: 0,
      },
      user.id,
    );

    // 为所有段落创建生成提示词的任务
    const segmentIds = projectDetails.segments.map(s => s.id);
    const task = await taskApi.createGeneratePromptsTask({
      accountId: projectDetails.project.account_id,
      projectId: data.projectId,
      segmentIds: segmentIds,
      style: data.style,
      context: data.context,
      priority: 'normal',
    });

    return {
      success: true,
      taskId: task.id,
      message: '已启动处理任务，正在生成图像提示词...',
    };
  },
  {
    auth: true,
    schema: StartProcessingSchema,
  },
);

/**
 * 暂停处理项目
 * Pause project processing
 */
export const pauseProcessingAction = enhanceAction(
  async function (data: PauseProcessingInput, user) {
    const client = getSupabaseServerClient();
    const api = createNovel2VideoApi(client);

    // 这里应该停止后台处理任务
    // 目前只是更新状态为草稿
    await api.updateProject(data.projectId, { status: 'draft' }, user.id);

    return {
      success: true,
      message: '已暂停项目处理',
    };
  },
  {
    auth: true,
    schema: PauseProcessingSchema,
  },
);