/**
 * 工具函数：将aspectRatio和分辨率转换为具体的宽度和高度
 */
export function getImageDimensions(aspectRatio: string, resolution: string) {
  // 基于分辨率的基础高度
  const baseHeights: Record<string, number> = {
    '480p': 480,
    '720p': 720,
    '1080p': 1080,
    '1440p': 1440,
    '2160p': 2160,
  };

  const height = baseHeights[resolution] || 1080;

  // 根据宽高比计算宽度
  switch (aspectRatio) {
    case '16:9':
      return { width: Math.round(height * 16 / 9), height };
    case '9:16':
      return { width: Math.round(height * 9 / 16), height };
    case '4:3':
      return { width: Math.round(height * 4 / 3), height };
    case '1:1':
      return { width: height, height };
    default:
      return { width: Math.round(height * 16 / 9), height }; // 默认16:9
  }
}

/**
 * 格式化尺寸显示
 */
export function formatDimensions(width: number, height: number): string {
  return `${width} × ${height}`;
}

/**
 * 获取宽高比的显示名称
 */
export function getAspectRatioDisplayName(aspectRatio: string): string {
  const names: Record<string, string> = {
    '16:9': '横屏 (16:9)',
    '9:16': '竖屏 (9:16)',
    '4:3': '传统 (4:3)',
    '1:1': '正方形 (1:1)',
  };
  return names[aspectRatio] || aspectRatio;
}