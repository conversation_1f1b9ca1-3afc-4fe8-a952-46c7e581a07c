'use client';

import { useState, useTransition, useCallback } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  FileTextIcon,
  UploadIcon,
  Cross2Icon,
  CheckCircledIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  InfoCircledIcon,
  LightningBoltIcon,
} from '@radix-ui/react-icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Progress } from '@kit/ui/progress';
import { toast } from '@kit/ui/sonner';
import { Spinner } from '@kit/ui/spinner';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { createProjectAction } from '../_lib/server/server-actions';

// 表单验证模式
const CreateProjectSchema = z.object({
  title: z
    .string()
    .min(1, 'novel2video:createProject.errors.titleRequired')
    .max(255, 'novel2video:createProject.errors.titleTooLong'),
  description: z.string().optional(),
  text: z
    .string()
    .min(10, 'novel2video:createProject.errors.textTooShort')
    .max(50000, 'novel2video:createProject.errors.textTooLong'),
});

type CreateProjectFormData = z.infer<typeof CreateProjectSchema>;

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type StepType = 'intro' | 'info' | 'content' | 'review';

const steps: { key: StepType; title: string; description: string; icon: typeof FileTextIcon }[] = [
  {
    key: 'intro',
    title: '开始创作',
    description: '了解如何将文字转换为视频',
    icon: LightningBoltIcon,
  },
  {
    key: 'info',
    title: '项目信息',
    description: '设置项目基本信息',
    icon: FileTextIcon,
  },
  {
    key: 'content',
    title: '内容导入',
    description: '上传或输入文本内容',
    icon: UploadIcon,
  },
  {
    key: 'review',
    title: '确认创建',
    description: '检查信息并创建项目',
    icon: CheckCircledIcon,
  },
];

export function CreateProjectDialog({
  open,
  onOpenChange,
}: CreateProjectDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [textFile, setTextFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState<StepType>('intro');
  const workspace = useUserWorkspace();

  const form = useForm<CreateProjectFormData>({
    resolver: zodResolver(CreateProjectSchema),
    defaultValues: {
      title: '',
      description: '',
      text: '',
    },
  });

  // 处理文件验证
  const validateFile = (file: File): boolean => {
    // 检查文件类型
    if (!file.type.includes('text') && !file.name.endsWith('.txt')) {
      toast.error('请上传文本文件（.txt格式）');
      return false;
    }

    // 检查文件大小（最大5MB）
    if (file.size > 5 * 1024 * 1024) {
      toast.error('文件大小不能超过5MB');
      return false;
    }

    return true;
  };

  // 处理文件上传
  const handleFileUpload = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setTextFile(file);
    setUploadProgress(0);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 20;
      });
    }, 100);

    // 读取文件内容
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      form.setValue('text', content);

      // 如果没有标题，使用文件名作为标题
      if (!form.getValues('title')) {
        const filename = file.name.replace(/\.[^/.]+$/, '');
        form.setValue('title', filename);
      }

      toast.success('文件上传成功！');
    };
    reader.readAsText(file, 'UTF-8');
  }, [form]);

  // 处理文件输入变化
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragIn = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setDragActive(true);
    }
  }, []);

  const handleDragOut = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, [handleFileUpload]);

  // 重置表单
  const resetForm = useCallback(() => {
    form.reset();
    setTextFile(null);
    setUploadProgress(0);
    setDragActive(false);
    setCurrentStep('intro');
  }, [form]);

  // 步骤导航
  const canGoNext = () => {
    const values = form.getValues();
    switch (currentStep) {
      case 'intro':
        return true;
      case 'info':
        return !!values.title?.trim();
      case 'content':
        return !!values.text?.trim() && values.text.length >= 10;
      case 'review':
        return true;
      default:
        return false;
    }
  };

  const goNext = () => {
    const stepIndex = steps.findIndex(s => s.key === currentStep);
    if (stepIndex < steps.length - 1) {
      setCurrentStep(steps[stepIndex + 1].key);
    }
  };

  const goBack = () => {
    const stepIndex = steps.findIndex(s => s.key === currentStep);
    if (stepIndex > 0) {
      setCurrentStep(steps[stepIndex - 1].key);
    }
  };

  const currentStepIndex = steps.findIndex(s => s.key === currentStep);

  // 提交表单
  const onSubmit = (data: CreateProjectFormData) => {
    startTransition(async () => {
      try {
        const result = await createProjectAction({
          title: data.title,
          description: data.description,
          text: data.text,
        });

        if (result.success) {
          toast.success('项目创建成功！');
          onOpenChange(false);
          resetForm();
          // 触发项目列表重新加载
          window.location.reload();
        } else {
          toast.error(result.message || '创建失败，请重试');
        }
      } catch (error) {
        console.error('Error creating project:', error);
        toast.error('创建失败，请重试');
      }
    });
  };

  // 获取字符计数颜色
  const getCharCountColor = (count: number) => {
    if (count < 10) return 'text-destructive';
    if (count > 45000) return 'text-orange-500';
    return 'text-muted-foreground';
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <motion.div
            key="intro"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            className="text-center space-y-8 py-8"
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                repeatType: "reverse",
              }}
              className="mx-auto w-24 h-24 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center"
            >
              <LightningBoltIcon className="h-12 w-12 text-white" />
            </motion.div>

            <div className="space-y-4">
              <h2 className="text-2xl font-bold">将文字转化为精彩视频</h2>
              <p className="text-muted-foreground text-lg max-w-md mx-auto">
                通过AI技术，自动为您的小说、故事生成配图和语音，制作专业的视频内容
              </p>
            </div>

            <div className="grid gap-4 max-w-lg mx-auto">
              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-sm">1</span>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">智能分段分析</p>
                    <p className="text-sm text-muted-foreground">AI自动分析文本内容和情节</p>
                  </div>
                </div>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-green-600 font-semibold text-sm">2</span>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">生成配图音频</p>
                    <p className="text-sm text-muted-foreground">为每个段落生成匹配的图像和语音</p>
                  </div>
                </div>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <span className="text-purple-600 font-semibold text-sm">3</span>
                  </div>
                  <div className="text-left">
                    <p className="font-medium">合成最终视频</p>
                    <p className="text-sm text-muted-foreground">自动合成高质量的视频内容</p>
                  </div>
                </div>
              </Card>
            </div>
          </motion.div>
        );

      case 'info':
        return (
          <motion.div
            key="info"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileTextIcon className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">项目基本信息</h3>
              </div>

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      项目标题 *
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        data-test="project-title-input"
                        placeholder="为您的作品起个好名字..."
                        className="text-lg"
                      />
                    </FormControl>
                    <FormDescription>
                      项目标题将作为生成视频的主要标识
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      项目描述
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        className="min-h-[100px] resize-none"
                        {...field}
                        data-test="project-description-input"
                        placeholder="简单描述一下这个项目的内容、风格或特色..."
                      />
                    </FormControl>
                    <FormDescription>
                      描述信息将帮助AI更好地理解内容风格
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </motion.div>
        );

      case 'content':
        return (
          <motion.div
            key="content"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <UploadIcon className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">导入文本内容</h3>
              </div>

              {/* 文件上传区域 */}
              <motion.div
                className={`relative rounded-lg border-2 border-dashed p-8 text-center transition-all duration-300 ${
                  dragActive
                    ? 'border-primary bg-primary/10 scale-105'
                    : textFile
                    ? 'border-primary bg-primary/5'
                    : 'border-muted-foreground/25 hover:border-muted-foreground/50 hover:bg-muted/25'
                }`}
                whileHover={{ scale: 1.01 }}
                transition={{ duration: 0.2 }}
                onDragEnter={handleDragIn}
                onDragLeave={handleDragOut}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept=".txt,text/*"
                  onChange={handleFileInputChange}
                  className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                  data-test="text-file-input"
                />

                <AnimatePresence mode="wait">
                  {textFile ? (
                    <motion.div
                      key="uploaded"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      className="space-y-4"
                    >
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 0.5 }}
                      >
                        <CheckCircledIcon className="h-12 w-12 text-primary mx-auto" />
                      </motion.div>
                      <div>
                        <p className="font-medium text-primary text-lg">文件上传成功！</p>
                        <p className="text-sm text-muted-foreground">
                          {textFile.name} ({(textFile.size / 1024).toFixed(1)} KB)
                        </p>
                      </div>
                      {uploadProgress < 100 && (
                        <div className="w-full max-w-xs mx-auto">
                          <Progress value={uploadProgress} className="h-2" />
                          <p className="text-xs text-muted-foreground mt-1">上传中... {uploadProgress}%</p>
                        </div>
                      )}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setTextFile(null);
                          setUploadProgress(0);
                          form.setValue('text', '');
                        }}
                      >
                        <Cross2Icon className="mr-2 h-4 w-4" />
                        移除文件
                      </Button>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="upload"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      className="space-y-4"
                    >
                      <motion.div
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <UploadIcon className="h-12 w-12 text-muted-foreground mx-auto" />
                      </motion.div>
                      <div>
                        <p className="font-medium text-lg">
                          {dragActive ? '松开鼠标即可上传' : '拖拽文件到此处或点击上传'}
                        </p>
                        <p className="text-sm text-muted-foreground mt-2">
                          支持 .txt 文件，最大 5MB
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">或</span>
                </div>
              </div>

              {/* 文本输入区域 */}
              <FormField
                control={form.control}
                name="text"
                render={({ field }) => {
                  const charCount = field.value?.length || 0;
                  const charCountColor = getCharCountColor(charCount);

                  return (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel className="text-base font-medium">
                          直接输入文本内容
                        </FormLabel>
                        <motion.div
                          className={`text-sm font-medium ${charCountColor}`}
                          animate={{ scale: charCount > 0 ? [1, 1.1, 1] : 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {charCount.toLocaleString()} / 50,000
                        </motion.div>
                      </div>

                      <FormControl>
                        <Textarea
                          className="min-h-[200px] font-mono text-sm resize-none"
                          {...field}
                          data-test="project-text-input"
                          placeholder="在这里粘贴或输入您的小说/文章内容...

支持纯文本格式，字数限制 10-50,000 字"
                        />
                      </FormControl>

                      <FormDescription className="flex items-center space-x-2">
                        <InfoCircledIcon className="h-4 w-4" />
                        <span>
                          文本将自动分段处理，建议包含完整的情节或章节
                        </span>
                      </FormDescription>

                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
          </motion.div>
        );

      case 'review':
        const values = form.getValues();
        const charCount = values.text?.length || 0;
        const estimatedSegments = Math.ceil(charCount / 200);
        return (
          <motion.div
            key="review"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <CheckCircledIcon className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">确认项目信息</h3>
              </div>

              <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">项目标题</label>
                  <p className="text-base font-medium">{values.title || '未设置'}</p>
                </div>

                {values.description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">项目描述</label>
                    <p className="text-sm">{values.description}</p>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-muted-foreground">文本内容</label>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-sm">
                      字数: <span className="font-medium">{charCount.toLocaleString()}</span>
                    </span>
                    <span className="text-sm">
                      预计段落: <span className="font-medium">{estimatedSegments}</span>
                    </span>
                  </div>
                  {textFile && (
                    <p className="text-xs text-muted-foreground mt-1">
                      源文件: {textFile.name}
                    </p>
                  )}
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-2">
                  <InfoCircledIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100">接下来将会发生什么？</h4>
                    <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
                      <li>• 系统将自动分析并切分文本内容</li>
                      <li>• 提取文本中的人物和场景信息</li>
                      <li>• 为每个段落生成合适的图像描述</li>
                      <li>• 您可以在项目详情页面查看和调整生成设置</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold">创建新项目</DialogTitle>
          <DialogDescription>
            跟随引导，轻松创建您的第一个视频项目
          </DialogDescription>
        </DialogHeader>

        {/* 步骤指示器 */}
        <div className="px-1 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isActive = step.key === currentStep;
              const isCompleted = steps.findIndex(s => s.key === currentStep) > index;
              const StepIcon = step.icon;

              return (
                <div key={step.key} className="flex items-center">
                  <motion.div
                    className={`flex items-center space-x-3 ${
                      isActive ? 'text-primary' : isCompleted ? 'text-primary' : 'text-muted-foreground'
                    }`}
                    animate={{ scale: isActive ? 1.05 : 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                        isActive
                          ? 'border-primary bg-primary text-primary-foreground'
                          : isCompleted
                          ? 'border-primary bg-primary text-primary-foreground'
                          : 'border-muted-foreground/30 bg-background'
                      }`}
                      whileHover={{ scale: 1.1 }}
                      layout
                    >
                      {isCompleted ? (
                        <CheckCircledIcon className="h-5 w-5" />
                      ) : (
                        <StepIcon className="h-5 w-5" />
                      )}
                    </motion.div>
                    <div className="hidden sm:block">
                      <p className={`text-sm font-medium ${
                        isActive ? 'text-primary' : isCompleted ? 'text-primary' : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </p>
                      <p className="text-xs text-muted-foreground">{step.description}</p>
                    </div>
                  </motion.div>

                  {index < steps.length - 1 && (
                    <div className={`mx-4 h-px w-12 bg-border transition-colors ${
                      isCompleted ? 'bg-primary' : ''
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <div className="border-t pt-6" />

        <Form {...form}>
          <div className="min-h-[400px] overflow-y-auto px-1">
            <AnimatePresence mode="wait">
              {renderStepContent()}
            </AnimatePresence>
          </div>

          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={currentStepIndex === 0 ? () => { onOpenChange(false); resetForm(); } : goBack}
                disabled={isPending}
              >
                {currentStepIndex === 0 ? (
                  <>
                    <Cross2Icon className="mr-2 h-4 w-4" />
                    取消
                  </>
                ) : (
                  <>
                    <ChevronLeftIcon className="mr-2 h-4 w-4" />
                    上一步
                  </>
                )}
              </Button>

              <div className="flex items-center space-x-2">
                {currentStep === 'review' ? (
                  <Button
                    onClick={form.handleSubmit(onSubmit)}
                    disabled={isPending || !canGoNext()}
                    data-test="create-project-submit"
                    className="px-8"
                  >
                    {isPending ? (
                      <>
                        <Spinner className="mr-2 h-4 w-4" />
                        创建中...
                      </>
                    ) : (
                      <>
                        <CheckCircledIcon className="mr-2 h-4 w-4" />
                        创建项目
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={goNext}
                    disabled={!canGoNext()}
                    className="px-8"
                  >
                    下一步
                    <ChevronRightIcon className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Form>
      </DialogContent>
    </Dialog>
  );
}