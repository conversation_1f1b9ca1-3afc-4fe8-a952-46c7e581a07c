'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  CalendarIcon,
  DotsHorizontalIcon,
  FileTextIcon,
  PlayIcon,
  VideoIcon,
  CheckCircledIcon,
  ExclamationTriangleIcon,
  ClockIcon,
} from '@radix-ui/react-icons';
import { motion } from 'framer-motion';

import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Progress } from '@kit/ui/progress';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';

import { getProjectsAction } from '../_lib/server/server-actions';

type ProjectData = {
  id: string;
  title: string;
  description: string | null;
  status: 'draft' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  segmentCount?: number;
};

const statusConfig = {
  draft: {
    label: 'novel2video:status.project.draft',
    variant: 'secondary' as const,
    icon: FileTextIcon,
    color: 'text-muted-foreground',
  },
  processing: {
    label: 'novel2video:status.project.processing',
    variant: 'default' as const,
    icon: ClockIcon,
    color: 'text-blue-600',
  },
  completed: {
    label: 'novel2video:status.project.completed',
    variant: 'default' as const,
    icon: CheckCircledIcon,
    color: 'text-green-600',
  },
  failed: {
    label: 'novel2video:status.project.failed',
    variant: 'destructive' as const,
    icon: ExclamationTriangleIcon,
    color: 'text-red-600',
  },
} as const;

export function ProjectsList() {
  const workspace = useUserWorkspace();
  const accountId = workspace.user.id;
  const [projects, setProjects] = useState<ProjectData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const result = await getProjectsAction({});
        if (result.success) {
          setProjects(
            result.data.map((project) => ({
              id: project.id,
              title: project.title,
              description: project.description,
              status: project.status,
              progress: project.progress,
              created_at: project.created_at,
              segmentCount: 0, // TODO: 从segments表计算
            })),
          );
        }
      } catch (error) {
        console.error('Error loading projects:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProjects();
  }, [accountId]);

  if (loading) {
    return (
      <div className="grid auto-rows-max gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: i * 0.1 }}
          >
            <Card className="h-64">
              <CardHeader className="pb-3">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-4 w-10" />
                </div>
                <Skeleton className="h-2 w-full" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-8 w-full" />
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <motion.div
        className="flex h-full items-center justify-center"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="space-y-6 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-muted/50"
          >
            <VideoIcon className="h-12 w-12 text-muted-foreground" />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-2"
          >
            <h3 className="text-xl font-semibold">
              <Trans i18nKey="novel2video:projects.empty.title" />
            </h3>
            <p className="text-muted-foreground max-w-md text-sm">
              <Trans i18nKey="novel2video:projects.empty.description" />
            </p>
          </motion.div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="grid auto-rows-max gap-6 pb-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {projects.map((project, index) => (
        <ProjectCard
          key={project.id}
          project={project}
          index={index}
        />
      ))}
    </motion.div>
  );
}

interface ProjectCardProps {
  project: ProjectData;
  index: number;
}

function ProjectCard({ project, index }: ProjectCardProps) {
  const StatusIcon = statusConfig[project.status].icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -4 }}
    >
      <Card className="group relative h-64 overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary/10">
        {/* Background gradient effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

        <CardHeader className="relative pb-3">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1">
              <motion.div
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                <CardTitle className="line-clamp-2 text-base font-semibold leading-tight">
                  {project.title}
                </CardTitle>
              </motion.div>
              <If condition={Boolean(project.description)}>
                <p className="mt-2 line-clamp-2 text-xs text-muted-foreground leading-relaxed">
                  {project.description}
                </p>
              </If>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                  >
                    <DotsHorizontalIcon className="h-4 w-4" />
                  </Button>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                  <Link href={`/home/<USER>/projects/${project.id}`}>
                    <PlayIcon className="mr-2 h-4 w-4" />
                    <Trans i18nKey="novel2video:actions.open" />
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileTextIcon className="mr-2 h-4 w-4" />
                  <Trans i18nKey="novel2video:actions.duplicate" />
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive">
                  <ExclamationTriangleIcon className="mr-2 h-4 w-4" />
                  <Trans i18nKey="novel2video:actions.delete" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="relative flex flex-1 flex-col justify-between space-y-4 pb-4">
          {/* Status and progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-2"
              >
                <StatusIcon className={`h-4 w-4 ${statusConfig[project.status].color}`} />
                <Badge
                  variant={statusConfig[project.status].variant}
                  className="text-xs font-medium"
                >
                  <Trans i18nKey={statusConfig[project.status].label} />
                </Badge>
              </motion.div>
              <span className="text-xs font-medium text-muted-foreground">
                {project.progress}%
              </span>
            </div>

            <If condition={project.progress > 0}>
              <div className="relative">
                <Progress value={project.progress} className="h-2" />
                <motion.div
                  className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-primary/5"
                  initial={{ width: 0 }}
                  animate={{ width: `${project.progress}%` }}
                  transition={{ duration: 1, delay: index * 0.1 }}
                />
              </div>
            </If>
          </div>

          {/* Project information */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <motion.div
              className="flex items-center space-x-1"
              whileHover={{ scale: 1.05 }}
            >
              <FileTextIcon className="h-3 w-3" />
              <span>
                <Trans
                  i18nKey="novel2video:projects.segmentCount"
                  values={{ count: project.segmentCount || 0 }}
                />
              </span>
            </motion.div>
            <motion.div
              className="flex items-center space-x-1"
              whileHover={{ scale: 1.05 }}
            >
              <CalendarIcon className="h-3 w-3" />
              <span>{new Date(project.created_at).toLocaleDateString()}</span>
            </motion.div>
          </div>

          {/* Action buttons */}
          <Button
            asChild
            className="w-full shadow-sm transition-all duration-200"
            size="sm"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link href={`/home/<USER>/projects/${project.id}`}>
                <motion.div
                  className="flex items-center justify-center"
                  whileHover={{ x: 2 }}
                >
                  <PlayIcon className="mr-2 h-4 w-4" />
                  <Trans i18nKey="novel2video:actions.open" />
                </motion.div>
              </Link>
            </motion.div>
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
