'use client';

import { useState } from 'react';

import {
  PlusIcon,
  VideoIcon,
} from '@radix-ui/react-icons';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@kit/ui/button';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { CreateProjectDialog } from './create-project-dialog';
import { ProjectsList } from './projects-list';

export function Novel2VideoDashboard() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  return (
    <motion.div
      className="flex h-full flex-col"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Simple top header bar */}
      <motion.div
        className="border-b bg-background/95 backdrop-blur"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container flex h-16 items-center justify-between px-6">
          <motion.div
            className="flex items-center space-x-3"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <motion.div
              className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10"
              whileHover={{
                scale: 1.1,
                rotate: 360,
                backgroundColor: 'rgb(var(--primary) / 0.2)'
              }}
              transition={{ duration: 0.3 }}
            >
              <VideoIcon className="h-4 w-4 text-primary" />
            </motion.div>
            <div>
              <h1 className="text-lg font-semibold tracking-tight">
                <Trans i18nKey="novel2video:dashboard.title" />
              </h1>
            </div>
          </motion.div>

          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Button
              onClick={() => setShowCreateDialog(true)}
              className="shadow-sm"
              data-test="create-project-button"
              asChild
            >
              <motion.button
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 10px 25px -5px rgba(var(--primary) / 0.3)"
                }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="flex items-center"
                  whileHover={{ x: 2 }}
                >
                  <motion.div
                    animate={{ rotate: showCreateDialog ? 45 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PlusIcon className="mr-2 h-4 w-4" />
                  </motion.div>
                  <Trans i18nKey="novel2video:dashboard.createProject" />
                </motion.div>
              </motion.button>
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Main content area */}
      <motion.div
        className="flex-1 overflow-hidden"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.7, delay: 0.3 }}
      >
        <div className="container h-full px-6 py-6">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <ProjectsList />
          </motion.div>
        </div>
      </motion.div>

      {/* Create project dialog */}
      <AnimatePresence mode="wait">
        <If condition={showCreateDialog}>
          <CreateProjectDialog
            open={showCreateDialog}
            onOpenChange={setShowCreateDialog}
          />
        </If>
      </AnimatePresence>
    </motion.div>
  );
}

