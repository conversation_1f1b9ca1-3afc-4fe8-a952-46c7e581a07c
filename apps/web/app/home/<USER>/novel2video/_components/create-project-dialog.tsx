'use client';

import { useState, useTransition, useCallback } from 'react';

import { useTranslation } from 'react-i18next';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  FileTextIcon,
  UploadIcon,
  Cross2Icon,
  CheckCircledIcon
} from '@radix-ui/react-icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Progress } from '@kit/ui/progress';
import { Separator } from '@kit/ui/separator';
import { toast } from '@kit/ui/sonner';
import { Spinner } from '@kit/ui/spinner';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { createProjectAction } from '../_lib/server/server-actions';

// 表单验证模式
const CreateProjectSchema = z.object({
  title: z
    .string()
    .min(1, 'novel2video:createProject.errors.titleRequired')
    .max(255, 'novel2video:createProject.errors.titleTooLong'),
  description: z.string().optional(),
  text: z
    .string()
    .min(10, 'novel2video:createProject.errors.textTooShort')
    .max(50000, 'novel2video:createProject.errors.textTooLong'),
});

type CreateProjectFormData = z.infer<typeof CreateProjectSchema>;

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateProjectDialog({
  open,
  onOpenChange,
}: CreateProjectDialogProps) {
  const { t } = useTranslation('novel2video');
  const [isPending, startTransition] = useTransition();
  const [textFile, setTextFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const workspace = useUserWorkspace();

  const form = useForm<CreateProjectFormData>({
    resolver: zodResolver(CreateProjectSchema),
    defaultValues: {
      title: '',
      description: '',
      text: '',
    },
  });

  // 处理文件验证
  const validateFile = (file: File): boolean => {
    // 检查文件类型
    if (!file.type.includes('text') && !file.name.endsWith('.txt')) {
      toast.error(
        <Trans i18nKey="novel2video:createProject.errors.invalidFileType" />,
      );
      return false;
    }

    // 检查文件大小（最大5MB）
    if (file.size > 5 * 1024 * 1024) {
      toast.error(
        <Trans i18nKey="novel2video:createProject.errors.fileTooLarge" />,
      );
      return false;
    }

    return true;
  };

  // 处理文件上传
  const handleFileUpload = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setTextFile(file);
    setUploadProgress(0);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + 20;
      });
    }, 100);

    // 读取文件内容
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      form.setValue('text', content);

      // 如果没有标题，使用文件名作为标题
      if (!form.getValues('title')) {
        const filename = file.name.replace(/\.[^/.]+$/, '');
        form.setValue('title', filename);
      }

      toast.success(
        <Trans i18nKey="novel2video:createProject.fileUploadSuccess" />
      );
    };
    reader.readAsText(file, 'UTF-8');
  }, [form]);

  // 处理文件输入变化
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragIn = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setDragActive(true);
    }
  }, []);

  const handleDragOut = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, [handleFileUpload]);

  // 重置表单
  const resetForm = useCallback(() => {
    form.reset();
    setTextFile(null);
    setUploadProgress(0);
    setDragActive(false);
  }, [form]);

  // 提交表单
  const onSubmit = (data: CreateProjectFormData) => {
    startTransition(async () => {
      try {
        const result = await createProjectAction({
          title: data.title,
          description: data.description,
          text: data.text,
        });

        if (result.success) {
          toast.success(
            <Trans i18nKey="novel2video:createProject.success.created" />,
          );
          onOpenChange(false);
          resetForm();
          // 可以触发项目列表重新加载
          window.location.reload();
        } else {
          toast.error(
            result.message || <Trans i18nKey="common:genericError" />,
          );
        }
      } catch (error) {
        console.error('Error creating project:', error);
        toast.error(<Trans i18nKey="common:genericError" />);
      }
    });
  };

  // 获取字符计数颜色
  const getCharCountColor = (count: number) => {
    if (count < 10) return 'text-destructive';
    if (count > 45000) return 'text-orange-500';
    return 'text-muted-foreground';
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="novel2video:createProject.title" />
          </DialogTitle>
          <DialogDescription>
            <Trans i18nKey="novel2video:createProject.description" />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 项目基本信息 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileTextIcon className="h-4 w-4" />
                <h3 className="text-lg font-semibold">
                  <Trans i18nKey="novel2video:createProject.projectInfo" />
                </h3>
              </div>

              {/* 项目标题 */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Trans i18nKey="novel2video:createProject.titleLabel" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        data-test="project-title-input"
                        placeholder={t('createProject.titlePlaceholder')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 项目描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Trans i18nKey="novel2video:createProject.descriptionLabel" />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        className="min-h-[80px]"
                        {...field}
                        data-test="project-description-input"
                        placeholder={t('createProject.descriptionPlaceholder')}
                      />
                    </FormControl>
                    <FormDescription>
                      <Trans i18nKey="novel2video:createProject.descriptionHint" />
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* 文件上传区域 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <UploadIcon className="h-4 w-4" />
                <h3 className="text-lg font-semibold">
                  <Trans i18nKey="novel2video:createProject.textImport" />
                </h3>
              </div>

              {/* 拖拽上传区域 */}
              <div
                className={`relative rounded-lg border-2 border-dashed p-6 text-center transition-colors ${dragActive
                  ? 'border-primary bg-primary/5'
                  : textFile
                    ? 'border-primary bg-primary/5'
                    : 'border-muted-foreground/25 hover:border-muted-foreground/50'
                  }`}
                onDragEnter={handleDragIn}
                onDragLeave={handleDragOut}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept=".txt,text/*"
                  onChange={handleFileInputChange}
                  className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                  data-test="text-file-input"
                />

                {textFile ? (
                  <div className="space-y-3">
                    <CheckCircledIcon className="h-8 w-8 text-primary mx-auto" />
                    <div>
                      <p className="font-medium text-primary">文件上传成功！</p>
                      <p className="text-sm text-muted-foreground">
                        {textFile.name} ({(textFile.size / 1024).toFixed(1)} KB)
                      </p>
                    </div>
                    {uploadProgress < 100 && (
                      <div className="w-full max-w-xs mx-auto">
                        <Progress value={uploadProgress} className="h-2" />
                        <p className="text-xs text-muted-foreground mt-1">上传中... {uploadProgress}%</p>
                      </div>
                    )}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setTextFile(null);
                        setUploadProgress(0);
                        form.setValue('text', '');
                      }}
                    >
                      <Cross2Icon className="mr-2 h-4 w-4" />
                      移除文件
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <UploadIcon className="h-8 w-8 text-muted-foreground mx-auto" />
                    <div>
                      <p className="font-medium">
                        {dragActive ? '松开鼠标即可上传' : '拖拽文件到此处或点击上传'}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        支持 .txt 文件，最大 5MB
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* 文本内容 */}
            <FormField
              control={form.control}
              name="text"
              render={({ field }) => {
                const charCount = field.value.length;
                const charCountColor = getCharCountColor(charCount);

                return (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel className="text-lg font-semibold">
                        <Trans i18nKey="novel2video:createProject.textLabel" />
                      </FormLabel>
                      <div className={`text-sm ${charCountColor}`}>
                        {charCount.toLocaleString()} / 50,000
                      </div>
                    </div>

                    <FormControl>
                      <Textarea
                        className="min-h-[300px] font-mono text-sm"
                        {...field}
                        data-test="project-text-input"
                        placeholder="在这里输入或粘贴你的小说/文章内容...

你可以：
• 直接在此输入文本
• 或者使用上方的文件上传功能
• 支持纯文本格式，字数限制 10-50,000 字"
                      />
                    </FormControl>

                    <div className="flex items-center justify-between">
                      <FormDescription>
                        <Trans
                          i18nKey="novel2video:createProject.textHint"
                          values={{
                            min: 10,
                            max: 50000,
                            current: charCount,
                          }}
                        />
                      </FormDescription>
                      {charCount > 0 && (
                        <div className="text-xs text-muted-foreground">
                          预计生成时长: {Math.ceil(charCount / 200)} 分钟
                        </div>
                      )}
                    </div>

                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  onOpenChange(false);
                  resetForm();
                }}
                disabled={isPending}
              >
                <Trans i18nKey="common:cancel" />
              </Button>
              <Button
                type="submit"
                disabled={isPending || form.watch('text').length < 10}
                data-test="create-project-submit"
              >
                {isPending ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    <Trans i18nKey="novel2video:createProject.creating" />
                  </>
                ) : (
                  <Trans i18nKey="novel2video:createProject.create" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}