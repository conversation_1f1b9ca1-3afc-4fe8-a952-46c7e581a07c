'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useUserWorkspace } from '@kit/accounts/hooks/use-user-workspace';
import { toast } from '@kit/ui/sonner';

type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
type TaskType = 'generate_prompts' | 'generate_images' | 'generate_audio' | 'merge_video';

interface Task {
  id: string;
  task_type: TaskType;
  status: TaskStatus;
  progress_percentage: number;
  progress_message?: string;
  error_message?: string;
  project_id: string;
  segment_id?: string;
  completed_at?: string;
  output_data?: any;
  result?: any; // 别名，为了向后兼容
}

interface UseTaskRealtimeProps {
  projectId: string;
  enabled?: boolean;
  onTaskComplete?: (task: Task) => void;
  onTaskUpdate?: (task: Task) => void;
  onRefreshData?: () => void;
  onBatchTaskComplete?: (tasks: Task[]) => void;
}

export function useTaskRealtime({
  projectId,
  enabled = true,
  onTaskComplete,
  onTaskUpdate,
  onRefreshData,
  onBatchTaskComplete,
}: UseTaskRealtimeProps) {
  const client = useSupabase();
  const queryClient = useQueryClient();
  const { user } = useUserWorkspace();
  
  // 使用 ref 避免闭包问题
  const completedTasksRef = useRef<Task[]>([]);
  const batchRefreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const firstBatchTaskTimeRef = useRef<number | null>(null);
  
  // 批量刷新的配置
  const BATCH_DELAY = 1000; // 1秒延迟
  const MAX_BATCH_WAIT = 3000; // 最大等待3秒，防止一直不刷新

  const handleBatchRefresh = useCallback(() => {
    if (completedTasksRef.current.length > 0) {
      console.log('🎉 Batch refresh triggered:', completedTasksRef.current.length, 'tasks');
      onRefreshData?.();
      onBatchTaskComplete?.(completedTasksRef.current);
      completedTasksRef.current = [];
      firstBatchTaskTimeRef.current = null;
    }
  }, [onRefreshData, onBatchTaskComplete]);

  const addToCompletedBatch = useCallback((task: Task) => {
    const now = Date.now();
    
    // 如果是第一个任务，记录时间
    if (completedTasksRef.current.length === 0) {
      firstBatchTaskTimeRef.current = now;
    }
    
    completedTasksRef.current.push(task);
    
    // 清除之前的定时器
    if (batchRefreshTimerRef.current) {
      clearTimeout(batchRefreshTimerRef.current);
    }
    
    // 检查是否达到最大等待时间
    const waitedTime = firstBatchTaskTimeRef.current ? now - firstBatchTaskTimeRef.current : 0;
    const remainingWaitTime = Math.max(0, MAX_BATCH_WAIT - waitedTime);
    const actualDelay = Math.min(BATCH_DELAY, remainingWaitTime);
    
    // 如果已经等待了最大时间，立即刷新
    if (remainingWaitTime <= 0) {
      handleBatchRefresh();
      return;
    }
    
    // 设置新的定时器
    batchRefreshTimerRef.current = setTimeout(() => {
      handleBatchRefresh();
    }, actualDelay);
    
    console.log(`📝 Added task to batch (${completedTasksRef.current.length} tasks), will refresh in ${actualDelay}ms`);
  }, [handleBatchRefresh]);

  useEffect(() => {
    if (!enabled || !projectId || !user?.id) {
      console.log('🔄 Task realtime disabled:', { enabled, projectId, userId: user?.id });
      return;
    }

    const channel = client.channel(`task-updates-${projectId}`);

    // 监听任务状态更新
    const subscription = channel
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'novel2video_tasks',
          filter: `project_id=eq.${projectId}`,
        },
        (payload) => {
          const task = payload.new as Task;
          
          console.log('🔄 Task update received:', {
            taskId: task.id,
            status: task.status,
            progress: task.progress_percentage,
            type: task.task_type
          });

          // 无论什么更新都调用更新回调
          onTaskUpdate?.(task);

          // 如果任务完成，显示通知并调用完成回调
          if (task.status === 'completed') {
            // 显示成功通知
            const taskTypeText = getTaskTypeText(task.task_type);
            toast.success(`${taskTypeText} 已完成`);
            
            onTaskComplete?.(task);
            
            // 对于segment相关的任务，使用批量刷新机制
            if (task.segment_id) {
              addToCompletedBatch(task);
            } else if (task.task_type === 'generate_prompts') {
              // 对于prompt生成任务，立即刷新
              onRefreshData?.();
            }
          } else if (task.status === 'failed') {
            // 显示失败通知
            const taskTypeText = getTaskTypeText(task.task_type);
            toast.error(`${taskTypeText} 失败: ${task.error_message || '未知错误'}`);
            
            // 失败时也需要刷新数据以显示最新状态
            if (task.task_type === 'generate_prompts' || task.segment_id) {
              onRefreshData?.();
            }
          }

          // 刷新相关查询
          queryClient.invalidateQueries({ 
            queryKey: ['project-tasks', projectId] 
          });
          
          // 对于React Query缓存的失效（如果有的话）
          if (task.status === 'completed' || task.status === 'failed') {
            if (task.task_type === 'generate_prompts' || task.segment_id) {
              queryClient.invalidateQueries({ 
                queryKey: ['project-data', projectId] 
              });
            }
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
      // 清理定时器
      if (batchRefreshTimerRef.current) {
        clearTimeout(batchRefreshTimerRef.current);
        batchRefreshTimerRef.current = null;
      }
      // 如果有未处理的任务，立即处理
      if (completedTasksRef.current.length > 0) {
        handleBatchRefresh();
      }
    };
  }, [enabled, projectId, user?.id, client, queryClient, onTaskComplete, onTaskUpdate, onRefreshData, addToCompletedBatch]);
  
  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      if (batchRefreshTimerRef.current) {
        clearTimeout(batchRefreshTimerRef.current);
        batchRefreshTimerRef.current = null;
      }
      // 组件卸载时如果有未处理的任务，立即处理
      if (completedTasksRef.current.length > 0) {
        handleBatchRefresh();
      }
    };
  }, [handleBatchRefresh]);
}

// 获取任务类型的显示文本
function getTaskTypeText(taskType: TaskType): string {
  const taskTypeMap: Record<TaskType, string> = {
    'generate_prompts': '图像提示词生成',
    'generate_images': '图像生成',
    'generate_audio': '音频生成',
    'merge_video': '视频合并',
  };
  
  return taskTypeMap[taskType] || taskType;
} 