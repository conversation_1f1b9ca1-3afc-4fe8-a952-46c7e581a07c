import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const audioPath = path.join('/');
    
    if (!audioPath) {
      return NextResponse.json({ error: 'Audio path is required' }, { status: 400 });
    }

    // 从 Supabase Storage 获取音频文件
    const supabase = getSupabaseServerAdminClient();
    
    // 音频存储在 'generated-audio' bucket 中
    const { data, error } = await supabase.storage
      .from('generated-audio')
      .download(audioPath);

    if (error || !data) {
      console.error('Error fetching audio:', error);
      return NextResponse.json({ error: 'Audio not found' }, { status: 404 });
    }

    // 获取文件类型
    const contentType = getContentType(audioPath);
    
    return new NextResponse(data, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
        'Accept-Ranges': 'bytes',
      },
    });
  } catch (error) {
    console.error('Error serving audio:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function getContentType(path: string): string {
  const extension = path.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'mp3':
      return 'audio/mpeg';
    case 'wav':
      return 'audio/wav';
    case 'ogg':
      return 'audio/ogg';
    case 'm4a':
      return 'audio/mp4';
    case 'aac':
      return 'audio/aac';
    case 'flac':
      return 'audio/flac';
    default:
      return 'audio/mpeg'; // 默认类型
  }
} 