import { NextResponse } from 'next/server';

import { z } from 'zod';

import { createComfyUIService } from '@kit/comfyui/service';
import type { TextToImageInput } from '@kit/comfyui/types';
import { enhanceRouteHandler } from '@kit/next/routes';

const GenerateImageSchema = z.object({
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
  width: z.number().min(64).max(2048).optional().default(512),
  height: z.number().min(64).max(2048).optional().default(512),
  steps: z.number().min(1).max(100).optional().default(20),
  cfg: z.number().min(1).max(30).optional().default(7),
  seed: z.number().optional(),
  model: z.string().optional(),
});

export const POST = enhanceRouteHandler(
  async function ({ body, user, request }) {
    try {
      const comfyUIService = createComfyUIService();

      const input: TextToImageInput = {
        prompt: body.prompt,
        negativePrompt: body.negativePrompt,
        width: body.width,
        height: body.height,
        steps: body.steps,
        cfg: body.cfg,
        seed: body.seed || Math.floor(Math.random() * 1000000),
        model: body.model,
      };

      const result = await comfyUIService.textToImage(input, {
        timeout: 120000,
        retries: 2,
      });

      if (!result.success) {
        return NextResponse.json(
          { error: result.error || 'Failed to generate image' },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        promptId: result.promptId,
        images: result.data,
      });
    } catch (error) {
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  {
    auth: true,
    schema: GenerateImageSchema,
  },
);

// 获取 ComfyUI 状态
export const GET = enhanceRouteHandler(
  async function ({ user, request }) {
    try {
      const comfyUIService = createComfyUIService();

      const result = await comfyUIService.getSystemInfo();

      if (!result.success) {
        return NextResponse.json(
          { error: result.error || 'Failed to get status' },
          { status: 500 },
        );
      }

      return NextResponse.json({
        success: true,
        data: result.data,
      });
    } catch (error) {
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 },
      );
    }
  },
  {
    auth: true,
  },
);
