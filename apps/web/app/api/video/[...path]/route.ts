import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const videoPath = path.join('/');
    
    if (!videoPath) {
      return NextResponse.json({ error: 'Video path is required' }, { status: 400 });
    }

    // 从 Supabase Storage 获取视频文件
    const supabase = getSupabaseServerAdminClient();
    
    // 假设视频存储在 'generated-videos' bucket 中
    const { data, error } = await supabase.storage
      .from('generated-videos')
      .download(videoPath);

    if (error || !data) {
      console.error('Error fetching video:', error);
      return NextResponse.json({ error: 'Video not found' }, { status: 404 });
    }

    // 获取文件类型
    const contentType = getVideoContentType(videoPath);
    
    // 转换 Blob 为 ArrayBuffer
    const arrayBuffer = await data.arrayBuffer();
    
    // 设置适当的响应头
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': arrayBuffer.byteLength.toString(),
      'Cache-Control': 'public, max-age=31536000, immutable', // 1年缓存
      'Accept-Ranges': 'bytes', // 支持范围请求，用于视频播放
    });

    // 处理范围请求（用于视频流播放）
    const range = request.headers.get('Range');
    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0] || '0', 10);
      const end = parts[1] ? parseInt(parts[1], 10) : arrayBuffer.byteLength - 1;
      const chunkSize = (end - start) + 1;
      
      const chunk = arrayBuffer.slice(start, end + 1);
      
      headers.set('Content-Range', `bytes ${start}-${end}/${arrayBuffer.byteLength}`);
      headers.set('Content-Length', chunkSize.toString());
      
      return new NextResponse(chunk, {
        status: 206, // Partial Content
        headers,
      });
    }

    return new NextResponse(arrayBuffer, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Error serving video:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getVideoContentType(filename: string): string {
  const ext = filename.toLowerCase().split('.').pop();
  
  if (!ext) {
    return 'video/mp4'; // 默认为 MP4
  }
  
  switch (ext) {
    case 'mp4':
      return 'video/mp4';
    case 'webm':
      return 'video/webm';
    case 'ogg':
      return 'video/ogg';
    case 'avi':
      return 'video/x-msvideo';
    case 'mov':
      return 'video/quicktime';
    case 'wmv':
      return 'video/x-ms-wmv';
    case 'flv':
      return 'video/x-flv';
    case 'mkv':
      return 'video/x-matroska';
    default:
      return 'video/mp4'; // 默认为 MP4
  }
} 